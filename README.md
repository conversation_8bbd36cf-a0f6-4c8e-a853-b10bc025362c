## 项目内容

这个 [`Turborepo`](https://turborepo.com/docs) 包含以下包/应用：

### 应用和包

- `stut`: 学生端, [Next.js](https://nextjs.org/)
- `teacher`: 教师端, [Next.js](https://nextjs.org/)
- `aipt`: 产课工具, [Next.js](https://nextjs.org/)
- `@repo/utils`: 在应用间复用的工具库
- `@repo/eslint-config`: `eslint` 配置 (包含 `eslint-config-next` 和 `eslint-config-prettier`)
- `@repo/typescript-config`: 整个 monorepo 使用的 `tsconfig.json`

每个包/应用都是 100% [TypeScript](https://www.typescriptlang.org/)。

### 构建说明

## ⚠️注意:
- 推送到 `dev` 分支会自动触发开发环境构建
- 推送到 `test` 分支会自动触发测试环境构建 
- 推送到 `main` 分支会自动触发生产环境构建
- 生产环境构建完成后需要手动点击发布
- 开发环境不走 CDN，测试和生产环境会走 CDN 加速


`local`：local 环境

  本地运行`pnpm sh [-F=aipt|stu|teacher]`构建发布local环境
  **Docker 部署脚本参数说明：**
  ```
  使用方法: pnpm sh -F=<aipt|stu|teacher> [选项]
  
  参数:
    -F, --filter=<app_name>     指定要构建的应用 (teacher, aipt, stu)
    -E, --env=<environment>     指定环境 (dev, test, prod) [默认: prod]
    -S, --server=<server>       指定服务器 (dev01, dev02) [默认: dev01]
    --deploy                    部署到远程服务器 [默认: true]
    --no-deploy                 只构建，不部署
    -h, --help                  显示帮助信息
  ```
dev01 地址：
- 学生端地址：[stu.local](http://stu.local.xiaoluxue.cn)
- 教师端地址：[teacher.local](http://teacher.local.xiaoluxue.cn)
- AI课生成工具：[aipt.local](http://aipt.local.xiaoluxue.cn)
- 运营管理平台：[admin.local](http://admin.local.xiaoluxue.cn/)

dev02 地址
- 学生端地址：[stu2.local](http://stu2.local.xiaoluxue.cn)
- 教师端地址：[teacher2.local](http://teacher2.local.xiaoluxue.cn)
- AI课生成工具：[aipt2.local](http://aipt2.local.xiaoluxue.cn)
- 运营管理平台：[admin2.local](http://admin2.local.xiaoluxue.cn/)

`dev`：开发环境
- 学生端地址：[stu.dev](https://stu.dev.xiaoluxue.cn/home)
- 教师端地址：[teacher.dev](https://teacher.dev.xiaoluxue.cn)
- AI课生成工具：[aipt.dev](https://aipt.dev.xiaoluxue.cn)

`test`：测试环境
- 学生端地址：[stu.test](https://stu.test.xiaoluxue.cn/home)
- 教师端地址：[teacher.test](https://teacher.test.xiaoluxue.cn)
- AI课生成工具：[aipt.test](https://aipt.test.xiaoluxue.cn)

`main`：⚠️ 生产环境，需要手动点击发布 ⚠️
- 学生端地址：[stu](https://stu.xiaoluxue.cn/home)
- 教师端地址：[teacher](https://teacher.xiaoluxue.cn)
- AI课生成工具地址：[aipt](https://aipt.xiaoluxue.cn)

[jenkins地址](https://jenkins.xiaoluxue.cn/)

### 环境变量说明
本地开发需配置 `.env` 文件，内容包括接口环境地址，无需上传至 gitlab。
如需在不同环境发布，需联系 郝红飞 配置不同环境的 `.env` 文件，区分不同环境。

### 工具

这个 Turborepo 已经为你设置了一些额外的工具：

- [TypeScript](https://www.typescriptlang.org/) 用于静态类型检查
- [ESLint](https://eslint.org/) 用于代码检查
- [Prettier](https://prettier.io) 用于代码格式化

### 构建

要构建指定应用和包，请运行以下命令：

```
cd schoolroom
pnpm build -F=<aipt|stu|teacher|admin>
pnpm build:dev -F=<aipt|stu|teacher|admin>
pnpm build:test -F=<aipt|stu|teacher|admin>
pnpm build:prod -F=<aipt|stu|teacher|admin>
```

### 开发

要开发指定应用和包，请运行以下命令：

```
cd schoolroom
pnpm dev -F=<aipt|stu|teacher|admin>
```

## 有用链接

了解更多关于 Turborepo 的强大功能：

- [任务](https://turbo.build/repo/docs/core-concepts/monorepos/running-tasks)
- [缓存](https://turbo.build/repo/docs/core-concepts/caching)
- [远程缓存](https://turbo.build/repo/docs/core-concepts/remote-caching)
- [过滤](https://turbo.build/repo/docs/core-concepts/monorepos/filtering)
- [配置选项](https://turbo.build/repo/docs/reference/configuration)
- [CLI 使用](https://turbo.build/repo/docs/reference/command-line-reference)

## QA
如果控制台打印 `API_HOST undefined`，需联系 郝红飞 检查相关环境变量是否配置.