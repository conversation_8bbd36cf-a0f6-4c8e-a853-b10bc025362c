"use client";

import { FC, useMemo, useCallback, memo } from "react";
import { useQuestionList } from "@/app/models/question-list-model";
import { exitLesson } from "@/app/utils/device";
import { CourseWidget } from "@/types/app/course";
import { StudyType } from "@repo/core/enums";
import { useClientContext } from "@/app/providers/client-provider";
import { ExerciseInitParams } from "@repo/core/new-exercise";
import { ExerciseView } from "@repo/core/new-exercise/view/main-view";
import Button from "@repo/ui/components/press-button";
import { useCourseViewContext } from "../course/course-view-context";
import { ExerciseHeaderActionView } from "../exercise/header-action-extra-view";
import {
  trackEvent,
  listenDeviceBackAction,
  surveillanceReport,
} from "@/app/utils/device";
import {
  getStudyBaseParamsFromUrl,
  getValueFromUrl,
} from "@repo/core/new-exercise/utils";

interface ExerciseInCourseViewProps {
  activeInCourse: boolean;
  widgetIndex: number;
  exerciseData: CourseWidget<"exercise">;
  onComplete: (totalTimeSpent?: number) => void;
  onBack?: () => void;
}

const EmptyView = memo(function EmptyView({
  onComplete,
  title,
  error,
  loading,
}: {
  onComplete: () => void;
  title: string;
  error?: Error;
  loading?: boolean;
}) {
  return (
    <div className="flex h-full w-full exercise-course-widget items-center justify-center">
      <div className="flex flex-col gap-4 items-center justify-center">
        {error ? (
          <div className="text-sm text-red-500">
            {title}: {error.message}
          </div>
        ) : (
          <div className="text-sm text-gray-500">{title}</div>
        )}
        {loading ? (
          <div className="text-sm text-gray-500">加载中...</div>
        ) : (
          <Button className="w-24" onClick={onComplete}>
            继续
          </Button>
        )}
      </div>
    </div>
  );
});

export const ExerciseInCourseView: FC<ExerciseInCourseViewProps> = ({
  activeInCourse,
  widgetIndex,
  exerciseData,
  onComplete,
  onBack = exitLesson, // ✅ 直接引用，避免每次创建新函数
}) => {
  const { lessonVersion } = useCourseViewContext();
  const { hasNextQuestion } = exerciseData.data; // ✅ 命名更短
  const clientContext = useClientContext();

  const { studySessionId } = useMemo(getStudyBaseParamsFromUrl, []);

  // ✅ 只有“无题目”时才请求题单；避免多余请求
  const shouldFetchPreview = !hasNextQuestion;
  const {
    data: fullData,
    isLoading,
    error,
  } = useQuestionList({
    studySessionId: shouldFetchPreview ? studySessionId : undefined,
    widgetIndex,
  });

  // ✅ 渲染函数稳定，减少子组件重渲染
  const renderHeaderRight = useCallback(
    ({
      questionStatus,
      questionId,
    }: {
      questionStatus: string;
      questionId: string;
    }) => {
      const isShowAskButton =
        questionStatus === "submitted" ||
        questionStatus === "second_answering" ||
        questionStatus === "evaluating";

      return (
        <ExerciseHeaderActionView
          questionId={questionId}
          showAskButton={isShowAskButton}
          showGuideButton
        />
      );
    },
    []
  );

  // ✅ 所有 ExerciseView 公共参数一次性 memo；deps 仅放参与构造的稳定引用
  const commonProps: ExerciseInitParams = useMemo(
    () => ({
      isExerciseActive: activeInCourse,
      widgetIndex,
      studySessionId,
      studyType: StudyType.AI_COURSE,
      onComplete,
      onBack,
      lessonVersion,
      // 覆盖部分 clientContext：外部透传 + 指定实现
      clientContext: {
        ...clientContext,
        trackEvent,
        listenDeviceBackAction,
        surveillanceReport,
      },
      displayConfig: {
        headerRight: {
          renderComponent: renderHeaderRight,
        },
      },
    }),
    [
      activeInCourse,
      widgetIndex,
      studySessionId,
      onComplete,
      onBack,
      lessonVersion,
      clientContext,
      renderHeaderRight,
    ]
  );

  // =============== 渲染分支收敛 ===============

  // A) 有题目 → 直接进入正式练习
  if (hasNextQuestion) {
    return <ExerciseView {...commonProps} />;
  }

  // B) 无题目 + 请求异常
  if (error) {
    return <EmptyView onComplete={onComplete} title="加载失败" error={error} />;
  }

  // C) 无题目 + 加载中（保持容器结构，避免闪烁）
  if (isLoading) {
    return <EmptyView onComplete={onComplete} title="加载中..." loading />;
  }

  // D) 无题目 + 加载完成，但无数据
  const questions = fullData?.questions ?? [];
  const studentAnswers = fullData?.studentAnswers ?? [];

  if (questions.length === 0) {
    return <EmptyView onComplete={onComplete} title="暂无练习数据" />;
  }

  // E) 预览模式（学生模式）
  return (
    <ExerciseView
      isPreview
      previewConfig={{
        studentMode: true,
        questionList: questions,
        studentAnswers,
        initialIndex: 0,
      }}
      {...commonProps}
    />
  );
};
