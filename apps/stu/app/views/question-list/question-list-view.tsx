import { getStatusBarHeight } from "@/app/utils/device";
import { useQuestionListViewModel } from "@/app/viewmodels/question-list";
import IconArrowRightSvg from "@/public/icons/ic_arrow_right.svg";
import IconCheckSvg from "@/public/icons/ic_right.svg";
import NoDataIconSvg from "@/public/icons/question-list-no-data.svg";
import { BackButton, FormatMath } from "@repo/core/new-exercise/components";
import PartiallyCorrectIconSvg from "@repo/core/public/assets/stu-exercise/icons/choice/ic_half_right.svg";
import CorrectIconSvg from "@repo/core/public/assets/stu-exercise/icons/choice/ic_right.svg";
import WrongIconSvg from "@repo/core/public/assets/stu-exercise/icons/choice/ic_wrong.svg";
import { useCallback, useRef } from "react";

export interface QuestionItem {
  id: number;
  type: string;
  content: string;
  status: "correct" | "partially-correct" | "wrong";
}

interface QuestionListProps {
  onBack: () => void;
}

interface OnlyWrongProps {
  onlyWrong: boolean;
  onChange: (checked: boolean) => void;
}

export const OnlyWrongCheckbox: React.FC<OnlyWrongProps> = ({
  onlyWrong,
  onChange,
}) => (
  <div className="flex pl-4 relative items-center justify-end">
    <input
      type="checkbox"
      name="question-list"
      id="wrong"
      checked={onlyWrong}
      onChange={(e) => onChange(e.target.checked)}
      className="cursor-pointer opacity-0 top-1/2 left-0 z-20 -translate-y-1/2 absolute"
    />
    {!onlyWrong ? (
      <span
        className="rounded-full border-[1px] border-[rgba(31,35,43,0.75)] h-4 mr-1 w-4 inline-block"
        onClick={() => onChange(!onlyWrong)}
      ></span>
    ) : (
      <IconCheckSvg onClick={() => onChange(!onlyWrong)} />
    )}
    <label
      htmlFor="wrong"
      className="cursor-pointer ml-1 text-[13px] select-none"
    >
      仅看错题
    </label>
  </div>
);

export const QuestionList: React.FC<QuestionListProps> = ({ onBack }) => {
  const {
    questions,
    isLoading,
    error,
    analysisId,
    handleViewAnalysis,
    handleCloseAnalysis,
    onlyWrong,
    handleOnlyWrongChange,
  } = useQuestionListViewModel();
  const statusBarHeight = useRef(getStatusBarHeight());

  const handleBack = useCallback(() => {
    if (onBack) {
      onBack();
    }
  }, [onBack]);

  return (
    <div
      className="bg-[var(--study-background)] flex flex-col h-screen question-list relative"
      style={{
        paddingTop: statusBarHeight.current + "px",
      }}
    >
      <div className="flex py-3 px-8 relative justify-between">
        <div className="flex h-10 w-[9rem] items-center">
          <BackButton onClick={handleBack} />
          <span className="font-medium ml-3 text-[17px]">题目列表</span>
        </div>
        <OnlyWrongCheckbox
          onlyWrong={onlyWrong}
          onChange={handleOnlyWrongChange}
        />
      </div>
      <div className="flex-1 overflow-auto">
        <div className="space-y-4 mx-20">
          {isLoading && questions.length === 0 ? (
            <div className="bg-white rounded-xl flex flex-col border-[1px] border-[rgba(51,46,41,0.06)] h-[calc(100vh-140px)] mx-10 mt-5 text-center mb-5 p-4 items-center justify-center">
              <span className="mt-4 text-[15px] text-gray-500">加载中...</span>
            </div>
          ) : questions.length > 0 ? (
            <>
              {questions.map((q, index) => (
                <div
                  key={q.questionId}
                  className="bg-white border rounded-lg border-[rgba(51,46,41,0.06)] mb-4"
                >
                  <div className="flex mb-2 p-6 items-start">
                    <span className="mr-2 text-gray-500">{index + 1}</span>
                    <span className="rounded bg-gray-100 flex-shrink-0 mr-2 text-sm py-0.5 px-2 text-gray-700">
                      {q.type}
                    </span>
                    <span className="font-medium -top-1 text-[17px] text-[#1F232B] relative line-clamp-2">
                      <FormatMath htmlContent={q.content} questionId={""} />
                    </span>
                  </div>
                  <p className="border-t-[1px] border-[#332E290F] h-[1px] mx-10"></p>
                  <div className="flex p-6 items-center">
                    {q.status === "correct" ? (
                      <span className="flex font-bold mr-4 text-green-600 items-center">
                        {/* <CorrectIconSvg className="h-5 mr-2 w-5" /> */}
                        回答正确
                      </span>
                    ) : q.status === "partially-correct" ? (
                      <span className="flex font-bold mr-4 text-yellow-600 items-center">
                        {/* <PartiallyCorrectIconSvg className="h-5 mr-2 w-5" /> */}
                        回答部分正确
                      </span>
                    ) : (
                      <span className="flex font-bold mr-4 text-red-600 items-center">
                        {/* <WrongIconSvg className="h-5 mr-2 w-5" /> */}
                        回答错误
                      </span>
                    )}
                    <button
                      className="flex ml-auto text-sm text-[rgba(51,48,45,0.85)] items-center hover:underline"
                      onClick={() => handleViewAnalysis(q.questionId)}
                    >
                      查看解析
                      <IconArrowRightSvg className="ml-1" />
                    </button>
                  </div>
                </div>
              ))}
            </>
          ) : (
            <div className="bg-white rounded-xl flex flex-col border-[1px] border-[rgba(51,46,41,0.06)] h-[calc(100vh-140px)] mx-10 mt-5 text-center mb-5 p-4 items-center justify-center">
              <div className="flex flex-col items-center">
                <NoDataIconSvg />
                <span className="mt-1 text-[15px]">{"暂无题目"}</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
