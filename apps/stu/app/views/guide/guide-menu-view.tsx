"use client";
import {
  IconButton,
  TranslucentGlassButton,
} from "@/app/components/guide/guide-buttons";
// import IconAsk from "@/public/icons/ask.svg";
import { useChatViewModel } from "@/app/viewmodels/chat/chat-viewmodel";
import { useAskQuestion } from "@/app/models/chat-model";
import IconBack from "@/public/icons/back.svg";
import IconAsk from "@/public/icons/icon-ask.svg";
import IconList from "@/public/icons/list.svg";
import { FC, useCallback, useEffect } from "react";
import { useCourseViewContext } from "../course/course-view-context";
import { useGuideViewContext } from "./guide-view-context";

export const GuideMenuView: FC<{
  chatViewModel: ReturnType<typeof useChatViewModel>;
}> = ({ chatViewModel }) => {
  const {
    isProgressBarOpen,
    exit,
    trackEventWithLessonId,
    showPlayerControls,
  } = useGuideViewContext();

  // const { currentQuestion } = useQuestionPreviewContext();
  // console.log("currentQuestion", currentQuestion.questionId);

  const handleOpenProgressBar = useCallback(() => {
    isProgressBarOpen.value = true;
    trackEventWithLessonId("lesson_progress_click");
  }, [isProgressBarOpen, trackEventWithLessonId]);

  const handleOpenChat = useCallback(() => {
    chatViewModel.handleOpen();
  }, [chatViewModel]);

  const { studySessionId } = useCourseViewContext();

  const { askData, askQuestion } = useAskQuestion({ studySessionId });

  useEffect(() => {
    askQuestion();
  }, [askQuestion]);

  if (!showPlayerControls.value) {
    return null;
  }

  return (
    <div className="flex flex-row h-11 w-full px-8 top-8 z-62 pointer-events-none absolute items-center justify-between">
      <IconButton icon={<IconBack />} onClick={exit} />
      <div className="flex flex-row gap-3 pointer-events-auto items-center">
        {askData?.isShow && (
          <TranslucentGlassButton
            icon={<IconAsk className="mr-0.5" />}
            className="bg-white rounded-lg h-10 opacity-100 outline-[0.50px] outline-offset-[-0.50px] outline-zinc-800/10 shadow-[0px_4px_16px_0px_rgba(35,42,64,0.05)] text-[rgba(51,48,45,0.7)] inline-flex items-center justify-center"
            onClick={handleOpenChat}
          >
            问一问
          </TranslucentGlassButton>
        )}
        <IconButton icon={<IconList />} onClick={handleOpenProgressBar} />
      </div>
    </div>
  );
};
