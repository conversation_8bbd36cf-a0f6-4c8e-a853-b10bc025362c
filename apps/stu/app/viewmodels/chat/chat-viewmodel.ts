import { toast } from "@/app/components/common/toast";
import useBridge from "@/app/hooks/use-bridge";
import {
  Conversation,
  InputStatus,
  useChatHistory,
  useSendChatMessage,
  useSuggestedQuestions,
  VoiceStatus,
} from "@/app/models/chat-model";
import { trackEvent } from "@/app/utils/device";
import { useCourseViewContext } from "@/app/views/course/course-view-context";
import { PlayerRef } from "@remotion/player";
import { useSearchParams } from "next/navigation";
import { RefObject, useCallback, useEffect, useRef, useState } from "react";

export function useChatViewModel(props?: {
  isOpen?: boolean;
  setIsOpen?: (isOpen: boolean) => void;
  currentComponentType?: string;
  questionId?: string;
  currentFrame?: number;
  index?: number;
  refPlayer?: RefObject<PlayerRef | null>;
  togglePlay?: () => void;
  isPlaying?: boolean;
  lessonVersion?: string;
}) {
  const { currentIndex } = useCourseViewContext();

  // 直接使用props中的数据，这些数据会从GuideViewContext中传递过来
  const refPlayer = props?.refPlayer;
  const isPlaying = props?.isPlaying ?? false;
  const togglePlay = props?.togglePlay;

  // 记录打开Chat前的播放状态
  const [wasPlayingBeforeOpen, setWasPlayingBeforeOpen] = useState(false);
  const searchParams = useSearchParams();
  // 从URL参数中获取必要信息
  const studySessionId = searchParams.get("studySessionId") || "0";
  const widgetIndex = currentIndex?.value || 0;
  const knowledgeId = searchParams.get("knowledgeId") || "0";
  const questionId = props?.questionId || searchParams.get("questionId") || "";
  const studyType = searchParams.get("studyType") || "0";
  const lessonId = searchParams.get("lessonId") || "0";
  const currentFrame = refPlayer?.current?.getCurrentFrame() || 0;
  const [isOpen, setIsOpen] = useState(props?.isOpen ?? false);
  const [messageList, setMessageList] = useState<Conversation[]>([]);
  const [initialMessage, setInitialMessage] = useState("");
  const [initialInputStatus, setInitialInputStatus] = useState<
    "text" | "voice"
  >("text");
  const [currentComponentType, setCurrentComponentType] = useState(
    props?.currentComponentType ?? "exercise"
  );
  const [suggestedQuestions, setSuggestedQuestions] = useState<
    { askText: string }[]
  >([]);
  const [remainingRounds, setRemainingRounds] = useState(50);
  const abortController = useRef<AbortController>(null);

  // SWR hooks
  const chatHistoryMutation = useChatHistory();
  const sendChatMutation = useSendChatMessage();
  const suggestedQuestionsMutation = useSuggestedQuestions();

  // 获取猜你想问
  const fetchSuggestedQuestions = useCallback(async () => {
    try {
      const { trigger } = suggestedQuestionsMutation;
      const data = await trigger({
        widgetIndex: widgetIndex,
        lessonId: Number(lessonId),
        questionId: questionId ? questionId : "",
        lessonVersion: props?.lessonVersion ?? "",
      });
      console.log("data", data);
      setSuggestedQuestions(data.list);
    } catch (error) {
      console.error("Failed to fetch suggested questions:", error);
      setSuggestedQuestions([]);
    }
  }, [
    suggestedQuestionsMutation,
    widgetIndex,
    questionId,
    lessonId,
    props?.lessonVersion,
  ]);

  // 优化的历史加载，减少处理时间
  const fetchHistory = useCallback(async () => {
    try {
      const { trigger } = chatHistoryMutation;
      const data = await trigger({
        studySessionId: Number(studySessionId),
        studyType: Number(studyType),
        widgetIndex: widgetIndex,
        knowledgeId: Number(knowledgeId),
        questionId: questionId ? questionId : "",
      });

      // 简化历史消息处理，减少映射操作的复杂度
      const historyMessages = data.list.map((item) => ({
        message: item.data,
        type: (item.type === "response" ? "dolli" : "user") as "dolli" | "user",
        time: item.time?.toString() ?? Date.now().toString(),
      }));

      // 直接设置消息列表，不做额外处理
      setRemainingRounds(data.remainingRounds ?? 50);
      setMessageList(historyMessages);
    } catch (error) {
      console.error("Failed to fetch chat history:", error);
      toast.show("哎呀，网络开小差了，请重试");
      setMessageList([]);
    }
  }, [
    chatHistoryMutation,
    studySessionId,
    studyType,
    widgetIndex,
    knowledgeId,
    questionId,
  ]);

  // 使用 useRef 存储函数引用，避免 useEffect 依赖问题
  const fetchHistoryRef = useRef(fetchHistory);
  const fetchSuggestedQuestionsRef = useRef(fetchSuggestedQuestions);

  // 更新 ref 中的函数引用
  useEffect(() => {
    fetchHistoryRef.current = fetchHistory;
    fetchSuggestedQuestionsRef.current = fetchSuggestedQuestions;
  }, [fetchHistory, fetchSuggestedQuestions]);

  useEffect(() => {
    if (isOpen) {
      fetchHistoryRef.current();
      fetchSuggestedQuestionsRef.current(); // 获取建议问题
      // props?.refPlayer?.current?.pause();
    } else {
      setMessageList([]);
      setSuggestedQuestions([]); // 清空建议问题
      // props?.refPlayer?.current?.play();
    }
  }, [isOpen, props?.refPlayer, props?.lessonVersion]); // 移除函数依赖，避免无限循环

  // 发送消息
  const handleSendMessage = useCallback(
    async (message: string, inputStatus: "text" | "voice") => {
      if (!message.trim()) return;
      abortController.current?.abort();
      abortController.current = null;
      trackEvent("qa_message_send", {
        lesson_id: studySessionId,
        widgetIndex: widgetIndex,
        widgetType: currentComponentType,
      });
      setMessageList((prev) => {
        // 先终止上一个 isTyping 的消息
        const interrupted = prev.map((msg, idx, arr) => {
          if (idx === arr.length - 1 && msg.isTyping) {
            return { ...msg, isTyping: false }; // 只设 isTyping: false，不加 isComplete
          }
          return msg;
        });
        // 再加新消息
        return [
          ...interrupted,
          { message, type: "user", time: Date.now().toString() },
          {
            message: "",
            type: "dolli",
            time: Date.now().toString(),
            placeholder: true,
          },
        ];
      });
      setInitialMessage("");
      // 真实请求
      const { trigger } = sendChatMutation;
      const [promise, controller] = trigger({
        studySessionId: Number(studySessionId),
        studyType: Number(studyType),
        widgetIndex: widgetIndex,
        CurrentFrame: questionId ? 0 : currentFrame,
        questionId: questionId ? questionId : "",
        knowledgeId: Number(knowledgeId),
        message,
        // method: inputStatus === "voice" ? 1 : 0,
      });
      abortController.current = controller;
      try {
        const stream = await promise;
        setRemainingRounds((prev) => prev - 1);
        for await (const chunk of stream) {
          if (controller.signal.aborted) {
            throw new Error("The user aborted a request.");
          }
          setMessageList((prev) => {
            const newList = [...prev];
            const last = newList.pop(); // 移除思考中
            if (!last) return [];
            return [
              ...newList,
              last?.placeholder
                ? {
                    message: chunk,
                    isTyping: true,
                    type: "dolli",
                    time: Date.now().toString(),
                  }
                : {
                    ...last,
                    message: last.message + chunk,
                  },
            ];
          });
        }
        setMessageList((prev) => {
          const newList = [...prev];
          const last = newList.pop();
          if (!last) return [];
          return [...newList, { ...last, isTyping: false, isComplete: true }];
        });
      } catch (e: any) {
        if (controller !== abortController.current) return; // 变化了代表不是一个请求了
        setMessageList((prev) => {
          const newList = [...prev];
          const last = newList.pop();
          if (!last) return [];
          if (last.placeholder) return newList;
          return [...newList, { ...last, isComplete: false, isTyping: false }];
        });
        if (!e?.message?.includes("user aborted")) {
          toast.error("哎呀，网络开小差了，请重试");
        }
      }

      if (abortController.current === controller) {
        abortController.current = null;
      }
    },
    [
      currentComponentType,
      currentFrame,
      knowledgeId,
      questionId,
      sendChatMutation,
      studySessionId,
      studyType,
      widgetIndex,
    ]
  );

  // 关闭
  const handleClose = useCallback(() => {
    setIsOpen(false);
    abortController.current?.abort();
    abortController.current = null;
    // 如果打开Chat前是播放状态，则恢复播放
    if (wasPlayingBeforeOpen && !isPlaying && togglePlay) {
      togglePlay();
    }
  }, [wasPlayingBeforeOpen, isPlaying, togglePlay]);

  // 打开
  const handleOpen = useCallback(() => {
    // 记录打开前的播放状态
    setWasPlayingBeforeOpen(isPlaying);
    setIsOpen(true);
    // 如果当前正在播放，则暂停
    if (isPlaying && togglePlay) {
      togglePlay();
    }
    trackEvent("qa_entry_click", {
      lesson_id: studySessionId,
      widgetIndex: widgetIndex,
      widgetType: currentComponentType,
    });
  }, [
    isPlaying,
    togglePlay,
    studySessionId,
    widgetIndex,
    currentComponentType,
  ]);

  // 更新消息（如 GuessQA）
  const handleUpdateMessage = useCallback(
    (message: string) => {
      handleSendMessage(message, initialInputStatus);
    },
    [handleSendMessage, initialInputStatus]
  );

  return {
    suggestedQuestions,
    isOpen,
    setIsOpen,
    messageList,
    setMessageList,
    initialMessage,
    initialInputStatus,
    handleSendMessage,
    handleUpdateMessage,
    handleClose,
    handleOpen,
    currentComponentType,
    remainingRounds,
  };
}

export function useSendMessageViewModel(props: {
  initialMessage?: string;
  initialInputStatus?: InputStatus;
  onSendMessage: (message: string, inputStatus: InputStatus) => void;
}) {
  const [inputStatus, setInputStatus] = useState<InputStatus>(
    props.initialInputStatus ?? "text"
  );
  const [voiceText, setVoiceText] = useState<string>(
    props.initialMessage ?? ""
  );
  const [voiceStatus, setVoiceStatus] = useState<VoiceStatus>("stop");
  const [permission, setPermission] = useState<string>();
  const [voiceToText, setVoiceToText] = useState<string>("请说话，点击发送");

  const { isSupported, callSync, callAsync } = useBridge();

  useEffect(() => {
    setInputStatus(props.initialInputStatus ?? "text");
  }, [props.initialInputStatus]);

  useEffect(() => {
    setVoiceText(props.initialMessage ?? "");
    return () => setVoiceText("");
  }, [props.initialMessage]);

  // 处理文本输入
  const onTextChange = useCallback((val: string) => {
    if (countChars(val.trim()) > 100) return;
    setVoiceText(val);
  }, []);

  // 切换输入模式
  const onSwitchMode = useCallback(() => {
    setInputStatus(inputStatus === "text" ? "voice" : "text");
    setVoiceToText("请说话，点击发送");
  }, [inputStatus]);

  // 发送消息
  const onSend = useCallback(() => {
    if (!voiceText.trim()) {
      return;
    }

    props.onSendMessage(voiceText, inputStatus);
    setVoiceText("");
  }, [voiceText, props.onSendMessage, inputStatus]);

  // 语音相关
  const onStartVoice = useCallback(async () => {
    if (!isSupported) return;
    try {
      const { granted } = await callAsync<any>("requestPermission", {
        permission: "android.permission.RECORD_AUDIO",
      });
      if (granted) {
        callSync("checkPermission", {
          permission: "android.permission.RECORD_AUDIO",
        });
        await callAsync("startAsrWithRecord");
        setVoiceStatus("start");
        setVoiceToText("请说话，点击发送");
      }
    } catch (error) {
      setPermission("无法获取麦克风权限");
    }
  }, [isSupported, callSync, callAsync]);

  const onStopVoice = useCallback(async () => {
    try {
      setVoiceToText("识别中，请稍后...");
      const { text } = await callAsync<{ text: string }>("finishTalking");
      let trimmedText = text.trim();
      if (trimmedText) {
        setVoiceStatus("stop");
        if (countChars(trimmedText) > 100) {
          toast.show("最多输入100字");
          while (countChars(trimmedText) > 100) {
            trimmedText = trimmedText.slice(0, -1);
          }
        }
        props.onSendMessage(trimmedText, inputStatus);
      } else {
        setVoiceToText("识别失败，请重试");
        setTimeout(() => setVoiceStatus("stop"), 1000);
      }
    } catch {
      setVoiceToText("识别失败，请重试");
    }
  }, [callAsync, props.onSendMessage, inputStatus]);

  const onCloseVoice = useCallback(() => {
    setVoiceStatus("stop");
  }, []);

  function countChars(text: string): number {
    let count = 0;

    // 使用正则匹配连续英文字母
    const englishWordRegex = /[a-zA-Z]+/g;

    // 找出所有连续英文字母的位置（按字符索引）
    const matches: { start: number; end: number }[] = [];
    let match: RegExpExecArray | null;
    while ((match = englishWordRegex.exec(text)) !== null) {
      matches.push({ start: match.index, end: match.index + match[0].length });
    }

    // 将字符串转为字符数组（支持 Unicode）
    const runeText = Array.from(text);
    const isInLongEnglishWord = new Array(runeText.length).fill(false);

    // 标记长度 > 1 的连续英文字母段
    for (const m of matches) {
      const runeStart = Array.from(text.slice(0, m.start)).length;
      const runeEnd = Array.from(text.slice(0, m.end)).length;

      if (runeEnd - runeStart > 1) {
        count += 2; // 整段加 2 分
        for (let i = runeStart; i < runeEnd; i++) {
          isInLongEnglishWord[i] = true;
        }
      }
    }

    // 遍历字符
    for (let i = 0; i < runeText.length; i++) {
      if (isInLongEnglishWord[i]) continue;

      const r = runeText[i];

      if (r === undefined) continue;

      // 汉字
      if (/[\u4e00-\u9fff]/.test(r)) {
        count += 1;
      }
      // 单个英文字母
      else if (/[a-zA-Z]/.test(r)) {
        count += 1;
      }
      // 特定符号 + - * =
      else if (r === "+" || r === "-" || r === "*" || r === "=") {
        count += 1;
      }
      // 数字
      else if (/[0-9]/.test(r)) {
        count += 1;
      }
      // 其他（标点、空格等）不计分
    }

    return count;
  }

  return {
    inputStatus,
    voiceText,
    voiceStatus,
    permission,
    voiceToText,
    onTextChange,
    onSwitchMode,
    onSend,
    onStartVoice,
    onStopVoice,
    onCloseVoice,
  };
}
