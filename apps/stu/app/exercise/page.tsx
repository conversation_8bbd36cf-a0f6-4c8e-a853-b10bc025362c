"use client";

import { StudyType } from "@repo/core/enums";
import {
  useEnterStudySession,
  useGetNextQuestion,
} from "@repo/core/new-exercise/models/exercise-model";
import { type ApiGetNextQuestionData } from "@repo/core/new-exercise/types";
import {
  getAllUrlParamsAsStrings,
  getStudyBaseParamsFromUrl,
  updateUrlParams,
} from "@repo/core/new-exercise/utils";
import { ExerciseView } from "@repo/core/new-exercise/view/main-view";
import { Suspense, useCallback, useEffect, useState } from "react";
import { ClientProvider, useClientContext } from "../providers/client-provider";
import {
  exitLesson,
  finishLesson,
  listenDeviceBackAction,
  setStatusBar,
  surveillanceReport,
  trackEvent,
} from "../utils/device";
import { useCourseViewContext } from "../views/course/course-view-context";
import { ExerciseHeaderActionView } from "../views/exercise/header-action-extra-view";

function ExercisePageContent() {
  const clientContext = useClientContext();
  const { currentIndex } = useCourseViewContext();

  const baseUrlParams = getStudyBaseParamsFromUrl();
  const _studyType = baseUrlParams.studyType;
  const _studySessionId = baseUrlParams.studySessionId;

  // 错题本相关状态
  const [initialQuestionData, setInitialQuestionData] =
    useState<ApiGetNextQuestionData | null>(null);
  const [isWrongBookInitialized, setIsWrongBookInitialized] = useState(false);

  // 错题本模式的 hooks
  const {
    enterSession,
    isEntering,
    enterError: _enterError,
  } = useEnterStudySession();
  const { getNextQuestion, isLoading: isLoadingQuestion } = useGetNextQuestion({
    studyType: _studyType || StudyType.REINFORCEMENT_EXERCISE,
    studySessionId: _studySessionId || 0,
  });

  useEffect(() => {
    setStatusBar({
      eventType: "setStatusBarVisibility",
      isVisible: false,
    });
  }, []);

  // 错题本初始化逻辑
  useEffect(() => {
    const isWrongQuestionBank = _studyType === StudyType.WRONG_QUESTION_BANK;

    if (isWrongQuestionBank && !isWrongBookInitialized) {
      setIsWrongBookInitialized(true);

      // 1. 获取当前 URL 的所有参数
      const urlParams = getAllUrlParamsAsStrings();

      // 2. 调用 useEnterStudySession
      enterSession(urlParams)
        .then((enterResponse) => {
          console.log("[错题本练习] useEnterStudySession 返回:", enterResponse);

          // 3. 将返回值更新到 URL
          if (enterResponse) {
            // 只更新已知的字符串或数字类型的参数
            const urlUpdateParams: Record<string, string | number | undefined> =
              {};
            if (typeof enterResponse.studySessionId === "number") {
              urlUpdateParams.studySessionId = enterResponse.studySessionId;
            }
            updateUrlParams(urlUpdateParams);
          }

          // 4. 调用 useGetNextQuestion
          return getNextQuestion();
        })
        .then((questionData) => {
          if (questionData) {
            setInitialQuestionData(questionData);
            console.log("[错题本练习] useGetNextQuestion 返回:", questionData);
          }
        })
        .catch((error) => {
          console.error("[错题本练习] 初始化失败:", error);
        });
    }
  }, [_studyType, isWrongBookInitialized, enterSession, getNextQuestion]);

  const handleBack = () => {
    exitLesson();
  };

  // 头部右侧渲染稳定回调，减少子树重渲染
  const renderHeaderRight = useCallback(
    ({
      questionStatus,
      questionId,
    }: {
      questionStatus: string;
      questionId: string;
    }) => {
      const showAsk =
        questionStatus === "submitted" ||
        questionStatus === "second_answering" ||
        questionStatus === "evaluating";

      return (
        <ExerciseHeaderActionView
          questionId={questionId}
          showAskButton={showAsk}
          showGuideButton={false}
          widgetIndex={currentIndex?.value || 0}
        />
      );
    },
    [currentIndex?.value]
  );

  // 如果是错题本模式且还在初始化中，显示加载状态
  if (
    _studyType === StudyType.WRONG_QUESTION_BANK &&
    (isEntering || isLoadingQuestion || !initialQuestionData)
  ) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="text-center">
          <div className="mb-2">错题本初始化中...</div>
          {isEntering && (
            <div className="text-sm text-gray-500">正在进入练习会话...</div>
          )}
          {isLoadingQuestion && (
            <div className="text-sm text-gray-500">正在获取题目...</div>
          )}
        </div>
      </div>
    );
  }

  return (
    <ExerciseView
      clientContext={{
        ...clientContext,
        trackEvent: trackEvent,
        listenDeviceBackAction: listenDeviceBackAction,
        surveillanceReport: surveillanceReport,
      }}
      studySessionId={_studySessionId || 0}
      studyType={_studyType || StudyType.REINFORCEMENT_EXERCISE}
      // 错题本模式传入初始化获取的题目数据
      initialQuestionData={
        _studyType === StudyType.WRONG_QUESTION_BANK
          ? initialQuestionData || undefined
          : undefined
      }
      onComplete={() => {
        finishLesson();
      }}
      onBack={handleBack}
      displayConfig={{
        headerRight: {
          renderComponent: renderHeaderRight,
        },
      }}
    />
  );
}

export default function ExercisePage() {
  return (
    <Suspense
      fallback={
        <div className="flex h-full w-full items-center justify-center">
          加载中...
        </div>
      }
    >
      <ExercisePageContent />
    </Suspense>
  );
}
