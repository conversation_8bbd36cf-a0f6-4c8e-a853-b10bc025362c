"use client";

import { useQuestionList } from "@/app/models/question-list-model";
import { StudyType } from "@repo/core/enums";
import {
  useEnterStudySession,
  useGetNextQuestion,
} from "@repo/core/new-exercise/models/exercise-model";
import { ApiGetNextQuestionData } from "@repo/core/new-exercise/types";
import {
  getAllUrlParamsAsStrings,
  updateUrlParams,
} from "@repo/core/new-exercise/utils";
import { ExerciseView } from "@repo/core/new-exercise/view/main-view";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useCallback, useEffect, useMemo, useState } from "react";
import { ClientProvider, useClientContext } from "../providers/client-provider";
import {
  finishLesson,
  listenDeviceBackAction,
  setStatusBar,
  surveillanceReport,
  trackEvent,
} from "../utils/device";
import { ExerciseHeaderActionView } from "../views/exercise/header-action-extra-view";

function ExercisePageContent() {
  const clientContext = useClientContext();

  // 直接从 searchParams 解析参数，避免 window.location 异步更新问题
  const searchParams = useSearchParams();
  const { studyType, studySessionId } = useMemo(() => {
    const studyTypeParam = searchParams.get("studyType");
    const studySessionIdParam = searchParams.get("studySessionId");

    console.log("[exercise-preview] 直接解析参数:", {
      studyTypeParam,
      studySessionIdParam,
      searchParamsString: searchParams.toString(),
    });

    // 解析 studyType
    let parsedStudyType = StudyType.REINFORCEMENT_EXERCISE; // 默认值
    if (studyTypeParam !== null && studyTypeParam.trim() !== "") {
      const studyTypeNum = Number(studyTypeParam);
      if (Object.values(StudyType).includes(studyTypeNum as StudyType)) {
        parsedStudyType = studyTypeNum as StudyType;
      }
    }

    // 解析 studySessionId
    const parsedStudySessionId =
      studySessionIdParam !== null ? parseInt(studySessionIdParam, 10) || 0 : 0;

    return {
      studyType: parsedStudyType,
      studySessionId: parsedStudySessionId,
    };
  }, [searchParams]);

  // 首渲染隐藏状态栏
  useEffect(() => {
    setStatusBar({ eventType: "setStatusBarVisibility", isVisible: false });
  }, []);

  const router = useRouter();
  const currentQuestionId = searchParams.get("questionId");
  const onlyWrong = searchParams.get("onlyWrong") === "true";
  const source = searchParams.get("source");

  // 错题本预览模式判断
  const isWrongQuestionPreview =
    studyType === StudyType.WRONG_QUESTION_BANK && currentQuestionId;

  // 错题本相关状态
  const [currentQuestionData, setCurrentQuestionData] =
    useState<ApiGetNextQuestionData | null>(null);
  const [isWrongBookInitialized, setIsWrongBookInitialized] = useState(false);

  // 错题本模式的 hooks
  const { enterSession, isEntering } = useEnterStudySession();
  const { getNextQuestion, isLoading: isLoadingCurrentQuestion } =
    useGetNextQuestion({
      studyType: studyType || StudyType.WRONG_QUESTION_BANK,
      studySessionId: studySessionId || studySessionId,
      questionId: currentQuestionId || undefined,
    });

  // 头部右侧渲染稳定回调，减少子树重渲染
  const renderHeaderRight = useCallback(
    ({
      questionStatus,
      questionId,
    }: {
      questionStatus: string;
      questionId: string;
    }) => {
      const showAsk =
        questionStatus === "submitted" ||
        questionStatus === "second_answering" ||
        questionStatus === "evaluating";

      return (
        <ExerciseHeaderActionView
          questionId={questionId}
          showAskButton={showAsk}
          showGuideButton
        />
      );
    },
    []
  );

  // 公共上下文：透传 + 指定实现，保持引用稳定
  const mergedClientContext = useMemo(
    () => ({
      ...clientContext,
      trackEvent,
      listenDeviceBackAction,
      surveillanceReport,
    }),
    [clientContext]
  );

  // 错题本预览模式初始化逻辑（简化版）
  useEffect(() => {
    if (
      isWrongQuestionPreview &&
      currentQuestionId &&
      !isWrongBookInitialized
    ) {
      setIsWrongBookInitialized(true);

      // 直接调用 enterSession 然后 getNextQuestion
      const urlParams = getAllUrlParamsAsStrings();

      enterSession(urlParams)
        .then((enterResponse) => {
          console.log("[错题本预览] enterSession 成功:", enterResponse);

          // 更新 URL 参数
          if (enterResponse?.studySessionId) {
            updateUrlParams({ studySessionId: enterResponse.studySessionId });
          }

          // 获取题目
          return getNextQuestion();
        })
        .then((data) => {
          if (data) {
            setCurrentQuestionData(data);
            console.log("[错题本预览] 题目获取成功:", data);
          }
        })
        .catch((error) => {
          console.error("[错题本预览] 初始化失败:", error);
        });
    }
  }, [
    isWrongQuestionPreview,
    currentQuestionId,
    isWrongBookInitialized,
    enterSession,
    getNextQuestion,
  ]);

  // 非错题本模式的原有逻辑（错题本预览模式不需要调用 useQuestionList）
  const {
    data: fullData,
    isLoading,
    error,
  } = useQuestionList({
    onlyWrong,
    studySessionId: isWrongQuestionPreview ? undefined : studySessionId, // 错题本预览模式不调用
  });

  const questionList = fullData?.questions || [];
  const studentAnswers = fullData?.studentAnswers || [];

  const handleBack = () => {
    console.log("handleBack");

    if (source) {
      router.replace(
        `/${source}?studySessionId=${studySessionId}&onlyWrong=${onlyWrong}`
      );
    } else {
      // 如果没有 source 参数，使用浏览器返回
      router.back();
    }
  };

  const handleQuestionChange = (data: {
    questionData: { questionId?: string } | null;
    index: number;
  }) => {
    if (data.questionData?.questionId) {
      const params = new URLSearchParams(searchParams);
      params.set("currentQuestionId", data.questionData.questionId);
      router.push(`/exercise-preview?${params.toString()}`);
    }
  };

  if (error) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="text-center">
          <div className="mb-2 text-red-500">加载失败</div>
          <div className="text-sm text-gray-500">{error.message}</div>
        </div>
      </div>
    );
  }

  // 错题本预览模式的加载状态
  if (
    isWrongQuestionPreview &&
    (isEntering || isLoadingCurrentQuestion || !currentQuestionData)
  ) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="text-center">
          <div className="mb-2">错题本预览初始化中...</div>
          {isEntering && (
            <div className="text-sm text-gray-500">正在进入练习会话...</div>
          )}
          {isLoadingCurrentQuestion && (
            <div className="text-sm text-gray-500">正在获取题目...</div>
          )}
        </div>
      </div>
    );
  }

  // 非错题本模式的加载状态
  if (!isWrongQuestionPreview && isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        加载中...
      </div>
    );
  }

  // 非错题本模式下，如果数据加载完成但题目列表为空，也需要等待或显示错误
  if (!isWrongQuestionPreview && !isLoading && questionList.length === 0) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <div className="text-center">
          <div className="mb-2 text-gray-500">暂无题目数据</div>
        </div>
      </div>
    );
  }

  return (
    <ExerciseView
      clientContext={mergedClientContext}
      studySessionId={studySessionId}
      studyType={studyType}
      onComplete={finishLesson}
      onBack={handleBack}
      displayConfig={{
        headerRight: { renderComponent: renderHeaderRight },
      }}
      // 始终是预览模式
      isPreview={true}
      // 错题本预览模式：使用从接口获取的题目数据
      initialQuestionData={
        isWrongQuestionPreview ? currentQuestionData || undefined : undefined
      }
      previewConfig={
        isWrongQuestionPreview
          ? undefined // 错题本预览模式不使用previewConfig
          : {
              questionId: currentQuestionId || undefined,
              questionList: questionList,
              studentAnswers: studentAnswers,
              initialIndex: 0,
            }
      }
      // 错题本预览模式下的题目切换回调
      onQuestionChange={
        isWrongQuestionPreview ? handleQuestionChange : undefined
      }
    />
  );
}

export default function ExercisePreviewPage() {
  return (
    <Suspense
      fallback={
        <div className="flex h-full w-full items-center justify-center">
          加载中...
        </div>
      }
    >
      <ClientProvider>
        <ExercisePageContent />
      </ClientProvider>
    </Suspense>
  );
}
