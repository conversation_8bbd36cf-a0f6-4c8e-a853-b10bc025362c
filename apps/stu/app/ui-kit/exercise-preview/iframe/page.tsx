"use client";

import { useIframeChild } from "@repo/core/components/iframe-preview/hooks/use-iframe-message";
import { StudyType } from "@repo/core/enums";
import {
  StudentAnsweredInfo,
  StudyQuestionInfo,
} from "@repo/core/new-exercise/types/question";
import { ExerciseView } from "@repo/core/new-exercise/view/main-view";
import { useState } from "react";

export default function IframePreviewPage() {
  const [questionList, setQuestionList] = useState<StudyQuestionInfo[]>([]);
  const [studentAnswers, setStudentAnswers] = useState<StudentAnsweredInfo[]>(
    []
  ); // 🆕 分离的学生答案状态
  const [studyType, setStudyType] = useState<StudyType>(
    StudyType.REINFORCEMENT_EXERCISE
  );

  // 使用 iframe 子页面 hooks
  const { requestClose } = useIframeChild({
    PREVIEW_DATA: (data) => {
      setQuestionList(data.questionList || []);
      setStudentAnswers(data.studentAnswers || []); // 🆕 接收分离的学生答案
      setStudyType(data.studyType || StudyType.REINFORCEMENT_EXERCISE);
    },
    UPDATE_STUDY_TYPE: (data) => {
      setStudyType(data.studyType);
    },
  });

  // 处理返回事件
  const handleBack = () => {
    requestClose();
  };

  const handleQuestionChange = (data: {
    questionData: StudyQuestionInfo | null;
    index: number;
  }) => {
    console.log("handleQuestionChange questionData", data.questionData);
    console.log("handleQuestionChange index", data.index);
  };

  if (questionList.length === 0) {
    return (
      <div className="flex h-full bg-gray-50 w-full iframe-loading items-center justify-center">
        <div className="text-center">
          <div className="font-medium text-lg mb-4 text-gray-600">
            等待数据加载...
          </div>
          <div className="text-sm text-gray-500">
            iframe 预览模式 (1000×600)
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-[600px] w-full">
      <ExerciseView
        isPreview={true}
        previewConfig={{
          questionList: questionList, // 🆕 使用合并后的数据
          studentAnswers: studentAnswers,
          initialIndex: 0,
        }}
        studySessionId={0}
        studyType={studyType}
        onBack={handleBack}
        clientContext={null}
        displayConfig={{
          progressBar: {
            hidden: studyType === StudyType.WRONG_QUESTION_BANK,
            // hidden: true,
          },
          timer: {
            hidden: studyType === StudyType.WRONG_QUESTION_BANK,
            // hidden: true,
          },
        }}
        onQuestionChange={handleQuestionChange}
      />
    </div>
  );
}
