"use client";

import { useSignal } from "@preact-signals/safe-react";
import { StudyType } from "@repo/core/enums";
import { StudyTypeThemeProvider } from "@repo/core/new-exercise/context/theme-context";
import { AnswerFeedbackType } from "@repo/core/new-exercise/enums";
import { NextQuestionGroupInfo } from "@repo/core/new-exercise/types/question";
import { TransitionView } from "@repo/core/new-exercise/view/transition-view";
import {
  TransitionState,
  useAnimationTransitionVM,
} from "@repo/core/new-exercise/viewmodels/animation-transition-vm";
import { useState } from "react";

export default function FeedbackDemoPage() {
  /* 转场动画状态 */
  const transitionState = useSignal<TransitionState>({
    queue: {
      items: [],
      currentIndex: -1,
      totalDuration: 0,
    },
    currentTransition: null,
    isPlayingTransitions: false,
  });

  /* 转场动画控制方法 */
  const updateTransitionState = (state: Partial<TransitionState>) => {
    transitionState.value = {
      ...transitionState.value,
      ...state,
    };
  };

  // 🔥 新的转场系统
  const transitionViewModel = useAnimationTransitionVM({
    questionStore: {
      transitionState,
      updateTransitionState,
    },
    trackEventWithExercise: () => {},
  });

  const { executeTransitionSequence, nextTransition, clearTransitionRecords } =
    transitionViewModel;

  // 题组模拟状态 - 使用 NextQuestionGroupInfo 格式
  const [groupSimulation, setGroupSimulation] = useState<{
    groups: NextQuestionGroupInfo[];
    currentGroupIndex: number;
  }>({
    groups: [
      {
        groupCount: 5,
        groupIndex: 0,
        groupName: "分式运算基础",
        groupQuestionCount: 8,
      },
      {
        groupCount: 5,
        groupIndex: 1,
        groupName: "分式方程应用",
        groupQuestionCount: 10,
        preGroupName: "分式运算基础",
      },
      {
        groupCount: 5,
        groupIndex: 2,
        groupName: "分式不等式",
        groupQuestionCount: 12,
        preGroupName: "分式方程应用",
      },
      {
        groupCount: 5,
        groupIndex: 3,
        groupName: "综合应用题",
        groupQuestionCount: 15,
        preGroupName: "分式不等式",
      },
      {
        groupCount: 5,
        groupIndex: 4,
        groupName: "压轴挑战题",
        groupQuestionCount: 6,
        preGroupName: "综合应用题",
      },
    ],
    currentGroupIndex: 0,
  });

  // 切换到下一个题组
  const switchToNextGroup = async () => {
    const nextIndex =
      (groupSimulation.currentGroupIndex + 1) % groupSimulation.groups.length;
    const currentGroup =
      groupSimulation.groups[groupSimulation.currentGroupIndex];
    const nextGroup = groupSimulation.groups[nextIndex];

    console.log("[Demo] 题组切换:", {
      currentIndex: groupSimulation.currentGroupIndex,
      nextIndex,
      currentGroup: currentGroup?.groupName,
      nextGroup: nextGroup?.groupName,
      totalGroups: groupSimulation.groups.length,
      preGroupName: currentGroup?.preGroupName,
    });

    // 显示题组转场
    if (nextGroup) {
      await executeTransitionSequence({
        groupInfo: {
          ...nextGroup,
          preGroupName: currentGroup?.groupName,
        },
      });
    }

    // 更新题组状态
    setGroupSimulation((prev) => ({
      ...prev,
      currentGroupIndex: nextIndex,
    }));

    console.log("[Demo] 题组转场完成");
  };

  // 显示单个反馈
  const showFeedback = async (
    type: AnswerFeedbackType,
    title: string,
    content: string
  ) => {
    console.log("[Demo] 显示单个反馈:", { type, title, content });

    await executeTransitionSequence({
      specialFeedbacks: [
        {
          type,
          title,
          content,
        },
      ],
    });

    console.log("[Demo] 单个反馈播放完成");
  };

  // 组合转场演示
  const showMultipleFeedbacks = async () => {
    console.log("[Demo] 组合转场演示开始");

    clearTransitionRecords();
    await executeTransitionSequence({
      groupInfo: {
        groupName: "新的题组",
        groupIndex: groupSimulation.currentGroupIndex + 1,
        groupCount: groupSimulation.groups.length,
        groupQuestionCount: 10,
      },
      specialFeedbacks: [
        {
          type: AnswerFeedbackType.AnswerCarelessly,
          title: "别气馁",
          content: "试着自己看看解析",
        },
        {
          type: AnswerFeedbackType.ContinuousCorrect,
          title: "连续正确！",
          content: "太棒了！连续答对3题！",
        },
      ],
    });

    console.log("[Demo] 组合转场演示完成");
  };

  const feedbackTypes: {
    type: AnswerFeedbackType;
    label: string;
    title: string;
    content: string;
    bgColor: string;
    description: string;
    autoCloseDelay?: number;
  }[] = [
    // 正确
    {
      type: AnswerFeedbackType.Correct,
      label: "正确反馈",
      title: "正确",
      content: "你太棒了！",
      bgColor: "bg-[#8B5CF6] hover:bg-[#7C3AED]",
      description: "正确答案的特殊反馈动效",
    },
    // 错误
    {
      type: AnswerFeedbackType.ContinuousCorrect,
      label: "连续正确反馈",
      title: "连续正确！",
      content: "你太棒了！连续答对多题！",
      bgColor: "bg-[#8B5CF6] hover:bg-[#7C3AED]",
      description: "连续多次正确答案的特殊反馈动效",
    },
    {
      type: AnswerFeedbackType.AnswerCarelessly,
      label: "粗心反馈",
      title: "认真作答",
      content: "有助于掌握度提升～",
      bgColor: "bg-[#F97316] hover:bg-[#EA580C]",
      description: "粗心错误时的反馈动效",
    },
    {
      type: AnswerFeedbackType.DifficultyUp,
      label: "难度升级反馈",
      title: "难度升级！",
      content: "迎接挑战吧！",
      bgColor: "bg-[#EC4899] hover:bg-[#DB2777]",
      description: "难度升级时的特殊反馈动效",
    },
    {
      type: AnswerFeedbackType.DifficultyDown,
      label: "难度下降反馈",
      title: "难度下降",
      content: "继续加油！",
      bgColor: "bg-[#0EA5E9] hover:bg-[#0284C7]",
      description: "难度下降时的特殊反馈动效",
    },
    {
      type: AnswerFeedbackType.Resume,
      label: "恢复练习转场动画",
      title: "空间几何~",
      content: "让我们继续练习",
      bgColor: "bg-[#6366F1] hover:bg-[#4F46E5]",
      description: "恢复练习转场动画",
      autoCloseDelay: 1400,
    },
  ];

  // 题组转场动画数据
  const groupTransitionData = {
    label: "题组转场动画",
    title: "新的题组",
    content: "让我们开始新的挑战",
    bgColor: "bg-[#14B8A6] hover:bg-[#0D9488]",
    description: "切换题组时的转场动画",
  };

  return (
    <div className="bg-gradient-to-br font-resource-han-rounded min-h-screen from-blue-50 to-indigo-100 p-8 feedback-demo-page">
      <div className="mx-auto max-w-4xl">
        {/* 页面标题 */}
        <div className="text-center mb-12 page-header">
          <h1 className="font-bold mb-4 text-4xl text-gray-800 main-title">
            答题反馈动效展示
          </h1>
          <p className="text-lg text-gray-600 subtitle">
            点击下方按钮查看不同类型的答题反馈动效和转场动画
          </p>
        </div>

        {/* 反馈类型网格 */}
        <div className="mb-12 grid gap-8 grid-cols-1 feedback-types-grid md:grid-cols-2">
          {feedbackTypes.map((feedback) => (
            <div
              key={feedback.type}
              className="bg-white rounded-2xl shadow-lg p-6 transition-shadow duration-300 feedback-type-card hover:shadow-xl"
            >
              <div className="mb-4 feedback-info">
                <h3 className="font-semibold text-xl mb-2 text-gray-800 feedback-label">
                  {feedback.label}
                </h3>
                <p className="text-sm text-gray-600 feedback-description">
                  {feedback.description}
                </p>
              </div>

              <div className="rounded-lg bg-gray-50 mb-4 p-4 feedback-preview">
                <div className="text-sm text-gray-700 feedback-text-preview">
                  <div className="main-text-preview">
                    <strong>主文字：</strong>
                    {feedback.title}
                  </div>
                  <div className="sub-text-preview">
                    <strong>鼓励文字：</strong>
                    {feedback.content}
                  </div>
                </div>
              </div>

              <button
                onClick={() =>
                  showFeedback(feedback.type, feedback.title, feedback.content)
                }
                className={`feedback-trigger-btn w-full rounded-lg px-6 py-3 font-medium text-white transition-colors duration-200 ${feedback.bgColor}`}
                disabled={transitionState.value.isPlayingTransitions}
              >
                {transitionState.value.isPlayingTransitions
                  ? "动效播放中..."
                  : `查看${feedback.label}`}
              </button>
            </div>
          ))}

          {/* 题组转场动画单独处理 */}
          <div className="bg-white rounded-2xl shadow-lg p-6 transition-shadow duration-300 group-transition-card hover:shadow-xl">
            <div className="mb-4 group-transition-info">
              <h3 className="font-semibold text-xl mb-2 text-gray-800 group-transition-label">
                {groupTransitionData.label}
              </h3>
              <p className="text-sm text-gray-600 group-transition-description">
                {groupTransitionData.description}
              </p>
            </div>

            <div className="rounded-lg bg-gray-50 mb-4 p-4 group-transition-preview">
              <div className="text-sm text-gray-700 group-transition-text-preview">
                <div className="group-main-text-preview">
                  <strong>主文字：</strong>
                  {groupTransitionData.title}
                </div>
                <div className="group-sub-text-preview">
                  <strong>鼓励文字：</strong>
                  {groupTransitionData.content}
                </div>
              </div>
            </div>

            <button
              onClick={switchToNextGroup}
              className={`group-transition-trigger-btn w-full rounded-lg px-6 py-3 font-medium text-white transition-colors duration-200 ${groupTransitionData.bgColor}`}
              disabled={transitionState.value.isPlayingTransitions}
            >
              {transitionState.value.isPlayingTransitions
                ? "动效播放中..."
                : `查看${groupTransitionData.label}`}
            </button>
          </div>
        </div>

        {/* 组合转场演示 */}
        <div className="bg-white rounded-2xl shadow-lg mb-8 p-6 multiple-transitions-demo">
          <h3 className="flex font-semibold text-xl mb-4 text-gray-800 demo-title items-center">
            <span className="rounded-full bg-[#8B5CF6] h-3 mr-3 w-3 demo-indicator"></span>
            组合转场演示
          </h3>
          <p className="mb-4 text-gray-600 demo-description">
            演示多个转场按顺序自动播放的效果，模拟真实答题场景中的连续转场
          </p>
          <button
            onClick={showMultipleFeedbacks}
            className="rounded-lg font-medium bg-[#8B5CF6] text-white w-full py-3 px-6 transition-colors duration-200 multiple-transitions-trigger-btn hover:bg-[#7C3AED]"
            disabled={transitionState.value.isPlayingTransitions}
          >
            {transitionState.value.isPlayingTransitions
              ? "组合转场播放中..."
              : "组合转场演示 (题组转场 → 特殊反馈)"}
          </button>
        </div>

        {/* 题组模拟切换 */}
        <div className="bg-white rounded-2xl shadow-lg mb-8 p-6">
          <h3 className="flex font-semibold text-xl mb-4 text-gray-800 items-center">
            <span className="rounded-full bg-[#14B8A6] h-3 mr-3 w-3"></span>
            题组模拟切换
          </h3>
          <div className="mb-4">
            <p className="mb-3 text-gray-600">
              模拟真实答题场景中的题组切换，每点击一次切换到下一个题组
            </p>
            <div className="rounded-lg bg-gray-50 mb-4 p-4">
              <div className="space-y-2 text-sm">
                <div>
                  <strong>当前题组:</strong>{" "}
                  {groupSimulation.currentGroupIndex + 1} /{" "}
                  {groupSimulation.groups.length}
                </div>
                <div>
                  <strong>题组名称:</strong>{" "}
                  {groupSimulation.groups[groupSimulation.currentGroupIndex]
                    ?.groupName || "未知题组"}
                </div>
                <div>
                  <strong>题目数量:</strong>{" "}
                  {groupSimulation.groups[groupSimulation.currentGroupIndex]
                    ?.groupQuestionCount || 0}{" "}
                  题
                </div>
                <div>
                  <strong>进度:</strong>
                  <div className="flex mt-2 gap-1 items-center">
                    {Array.from(
                      { length: groupSimulation.groups.length },
                      (_, index) => (
                        <div
                          key={index}
                          className={`h-4 w-4 rounded-full ${
                            index === groupSimulation.currentGroupIndex
                              ? "bg-[#14B8A6]"
                              : index < groupSimulation.currentGroupIndex
                                ? "bg-[#F4A267]"
                                : "bg-gray-200"
                          }`}
                        />
                      )
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <button
            onClick={switchToNextGroup}
            className="rounded-lg font-medium bg-[#14B8A6] text-white w-full py-3 px-6 transition-colors duration-200 hover:bg-[#0D9488]"
            disabled={transitionState.value.isPlayingTransitions}
          >
            {transitionState.value.isPlayingTransitions
              ? "题组转场播放中..."
              : "切换到下一个题组"}
          </button>
        </div>

        {/* 技术信息 */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>
            技术栈：React + TypeScript + Framer Motion + Tailwind CSS + Canvas
            API
          </p>
        </div>
      </div>

      <StudyTypeThemeProvider studyType={StudyType.AI_COURSE}>
        {/* 转场视图 */}
        <TransitionView
          currentTransition={transitionState.value.currentTransition || null}
          nextTransition={nextTransition}
        />
      </StudyTypeThemeProvider>
    </div>
  );
}
