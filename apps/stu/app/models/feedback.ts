import { get, post } from "@/app/utils/fetcher";
import useSWRMutation from "swr/mutation";

// 反馈类型枚举定义
export enum FeedbackType {
  AI = 101,
  Exercise = 102,
  EXPAND_EXERCISE = 105,
}

// 反馈类型定义（保持向后兼容）
export type FeedbackTypeString = string;

// 获取反馈标签配置的请求参数
export interface FeedbackConfigRequest {
  /**
   * 反馈类型 101-巩固练习 & ai课-练习组件  102-全局反馈
   */
  feedbackType?: FeedbackType;
}

// 反馈标签配置
export interface FeedbackTag {
  name: string;
  id: number;
  type: number;
}

// 反馈标签配置响应
export interface FeedbackConfigResponse {
  tags: FeedbackTag[];
  success: boolean;
  message?: string;
}

// 学生信息
export interface StudentInfo {
  studentId: string;
  studentName?: string;
  grade?: string;
  school?: string;
}

// 反馈提交数据
export interface FeedbackSubmitData {
  feedbackType: FeedbackType;
  feedbackLabels: number[];
  feedbackContent: string;
  feedbackKnowledgeId?: number;
  feedbackPhaseId?: number;
  feedbackLessonId?: number;
  feedbackWidgetIndex?: number;
  feedbackStudySessionId?: string;
  feedbackSubjectId?: number;
  feedbackSourceClientId?: number;
  feedbackStudentId?: number;
  feedbackGradeId?: number;
  feedbackTime?: number;
  feedbackDeviceInfo?: {
    os: string;
    deviceModel: string;
    appVersion: string;
    networkType: string;
  };
  feedbackQuestionId?: string;
}

// 反馈提交响应
export interface FeedbackSubmitResponse {
  success: boolean;
  message: string;
  feedbackId?: string;
}

// 工具函数
export function createFeedbackSubmitData(data: {
  feedbackType: FeedbackType;
  feedbackLabels: number[];
  feedbackContent: string;
  feedbackKnowledgeId?: number;
  feedbackPhaseId?: number;
  feedbackLessonId?: number;
  feedbackWidgetIndex?: number;
  feedbackStudySessionId?: string;
  feedbackSubjectId?: number;
  feedbackSourceClientId?: number;
  feedbackStudentId?: number;
  feedbackGradeId?: number;
  feedbackTime?: number;
  feedbackDeviceInfo?: {
    os: string;
    deviceModel: string;
    appVersion: string;
    networkType: string;
  };
  feedbackQuestionId?: string;
}): FeedbackSubmitData {
  return {
    ...data,
  };
}

const prefix = "/feedback-api";
// SWR Hooks
export function useSubmitFeedback() {
  return useSWRMutation<
    FeedbackSubmitResponse,
    Error,
    string,
    FeedbackSubmitData
  >("/api/v1/student/feedback/create", async (url, { arg }) => {
    const data = await post(url, { arg, prefix: "/feedback-api" });
    return data as FeedbackSubmitResponse;
  });
}

// 获取反馈标签配置的hook
export function useFeedbackConfig(params?: FeedbackConfigRequest) {
  const queryParams = params
    ? Object.entries(params).reduce(
        (acc, [key, value]) => {
          if (value !== undefined) {
            acc[key] = String(value);
          }
          return acc;
        },
        {} as Record<string, string>
      )
    : {};

  const queryString =
    Object.keys(queryParams).length > 0
      ? new URLSearchParams(queryParams).toString()
      : "";

  const baseUrl = "/api/v1/student/feedback/config";
  const url = queryString ? `${baseUrl}?${queryString}` : baseUrl;

  const { data, error, trigger, isMutating } =
    useSWRMutation<FeedbackConfigResponse>(
      url,
      async (url: string) => {
        const response = await get<FeedbackConfigResponse>(url, {
          query: undefined,
          prefix,
        });
        return response as FeedbackConfigResponse;
      },
      {
        onSuccess: (data) => {
          console.log("feedback config data", data);
        },
      }
    );

  return {
    config: data,
    tags: data?.tags || [],
    error,
    refreshConfig: trigger,
    isMutating,
  };
}
