import { get, stream, post } from "@/app/utils/fetcher";
import { useCallback } from "react";
import useSWRMutation from "swr/mutation";
import { z } from "zod";
// 消息类型定义
export interface Conversation {
  message: string;
  type: "user" | "dolli";
  time: string;
  isTyping?: boolean;
  isComplete?: boolean;
  method?: number;
  placeholder?: boolean;
}
export interface ChatHistoryItem {
  data: string;
  type: "user" | "response";
  time?: number | string;
  method?: number;
  [key: string]: unknown;
}
export interface ChatHistory {
  remainingRounds: number;
  list: ChatHistoryItem[];
}
export interface ChatPayload {
  studySessionId?: number;
  widgetIndex: number;
  studyType: number;
  CurrentFrame: number;
  questionId?: string;
  knowledgeId: number;
  message?: string;
  method?: number;
}
export interface ChatResponse {
  message: string;
}

export interface SuggestedQuestion {
  askText: string;
}

export interface SuggestedQuestionsResponse {
  list: SuggestedQuestion[];
}

export interface SuggestedQuestionsPayload {
  studySessionId: number;
  widgetIndex: number;
  knowledgeId: number;
  studyType: number;
  questionId?: string;
}

// 假数据
export const mockConversations: Conversation[] = [
  {
    message: "你好，有什么可以帮你？",
    type: "dolli",
    time: Date.now().toString(),
    method: 0,
  },
];

// mock API
export const ChatHistorySchema = z.object({
  remainingRounds: z.number(),
  history: z.array(
    z.object({
      data: z.string(),
      type: z.enum(["user", "response"]),
      time: z.union([z.string(), z.number()]).optional(),
      method: z.number().optional(),
    })
  ),
});

export function useChatHistory() {
  return useSWRMutation<
    ChatHistory,
    Error,
    string,
    {
      studySessionId: number;
      widgetIndex: number;
      knowledgeId: number;
      studyType: number;
      questionId?: string;
    }
  >("/api/v1/lesson/qa/history", async (url, { arg }) => {
    const data = await get(url, {
      query: {
        lessonId: String(arg.studySessionId),
        widgetIndex: String(arg.widgetIndex),
        knowledgeId: String(arg.knowledgeId),
        studyType: String(arg.studyType),
        studySessionId: String(arg.studySessionId),
        questionId: arg.questionId || "",
      },
      prefix: "/study-support-api",
    });
    // zod 校验后，类型强转
    const parsed = ChatHistorySchema.parse(data);
    return {
      remainingRounds: parsed.remainingRounds,
      list: parsed.history,
    };
  });
}

export const ChatResponseSchema = z.object({
  message: z.string(),
});

export function useSendChatMessage() {
  const trigger = useCallback(
    (arg: ChatPayload) =>
      stream("/api/v1/lesson/qa/chat/stream", {
        arg,
        prefix: "/study-support-api",
      }),
    []
  );
  return {
    trigger,
  };
}

export const SuggestedQuestionsResponseSchema = z.object({
  list: z.array(
    z.object({
      askText: z.string(),
    })
  ),
});
export function useSuggestedQuestions() {
  return useSWRMutation<
    SuggestedQuestionsResponse,
    Error,
    string,
    {
      widgetIndex: number;
      questionId?: string;
      lessonVersion?: string;
      lessonId: number;
    }
  >("/api/v1/lesson/qa/presupposed/ask", async (url, { arg }) => {
    const data = await get(url, {
      query: {
        lessonId: String(arg.lessonId),
        widgetIndex: String(arg.widgetIndex),
        questionId: arg.questionId || "",
        lessonVersion: arg.lessonVersion || "",
      },
      prefix: "/study-support-api",
    });
    // zod 校验后，类型强转
    const parsed = SuggestedQuestionsResponseSchema.parse(data);
    return {
      list: parsed.list,
    };
  });
}

export type InputStatus = "voice" | "text";
export type VoiceStatus = "start" | "stop";

export interface SendMessageState {
  inputStatus: InputStatus;
  voiceText: string;
  voiceStatus: VoiceStatus;
  permission?: string;
  voiceToText: string;
}

// 定义问一问接口的返回类型
interface AskQuestionResponse {
  isShow: boolean;
}

export function useAskQuestion(params: { studySessionId: number }) {
  const { trigger, isMutating, error, data } = useSWRMutation(
    "/api/v1/lesson/qa/is_show",
    async (url) => {
      const response = await get<AskQuestionResponse>(url, {
        query: { studySessionId: params.studySessionId.toString() },
        prefix: "/study-support-api",
      });
      return response;
    }
  );

  return {
    askQuestion: trigger,
    isAsking: isMutating,
    askError: error,
    askData: data,
  };
}

