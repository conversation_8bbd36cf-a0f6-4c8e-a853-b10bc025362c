import { ApiLessonWidget } from "@/types/api/lesson";
import {
  CourseWidget,
  CourseWidgetSummaryWithoutStatus,
} from "@/types/app/course";
import { ApiGetNextQuestionData } from "@repo/core/new-exercise/types";
import { GuideWidgetData } from "@repo/core/types/data/widget-guide";
import { VideoWidgetData } from "@repo/core/types/data/widget-video";
import useSWR from "swr";

const getApiUrl = ({
  knowledgeId,
  lessonId,
  summary,
  nextQuestionParams,
}: {
  knowledgeId: number;
  lessonId: number;
  summary?: CourseWidgetSummaryWithoutStatus;
  nextQuestionParams?: string;
}) => {
  if (!summary) {
    return null;
  }

  const { type, index, cdnUrl } = summary;

  if (type === "exercise") {
    return `/api/v1/study_session/next_question?widgetIndex=${index}&${nextQuestionParams}`;
  } else if (type == "guide" && cdnUrl) {
    return cdnUrl;
  } else {
    return `/api/v1/lesson/widget/info?knowledgeId=${knowledgeId}&lessonId=${lessonId}&widgetIndex=${index}`;
  }
};

const useCourseWidgetModel = ({
  knowledgeId,
  lessonId,
  summary,
  nextQuestionParams,
}: {
  knowledgeId: number;
  lessonId: number;
  summary?: CourseWidgetSummaryWithoutStatus;
  nextQuestionParams?: string;
}) => {
  const apiUrl = getApiUrl({
    knowledgeId,
    lessonId,
    summary,
    nextQuestionParams,
  });

  // 🔥 针对 exercise 类型的 next_question API 禁用所有重新验证
  const swrOptions =
    summary?.type === "exercise"
      ? {
          revalidateOnFocus: false, // 禁用焦点重新验证
          revalidateOnReconnect: false, // 禁用重连重新验证
          revalidateIfStale: false, // 禁用过期重新验证
          refreshWhenOffline: false, // 禁用离线刷新
          refreshWhenHidden: false, // 禁用隐藏时刷新
          dedupingInterval: 60000, // 60秒内相同请求去重
        }
      : {};

  const { data, isLoading, error, mutate } = useSWR<
    GuideWidgetData | ApiGetNextQuestionData | VideoWidgetData
  >(apiUrl, swrOptions);

  const parse = (
    data:
      | GuideWidgetData
      | ApiGetNextQuestionData
      | ApiLessonWidget
      | VideoWidgetData
      | undefined
  ) => {
    if (!summary || !data) {
      return undefined;
    }
    const { index, name, type, cdnUrl } = summary;

    if (type === "exercise") {
      return {
        index,
        name,
        type,
        data,
      } as CourseWidget<"exercise">;
    }

    if (type === "guide") {
      if (cdnUrl) {
        return {
          index,
          name,
          type,
          data,
        } as CourseWidget<"guide">;
      } else {
        // 兼容旧版，旧版没有cdnUrl
        const widgetData = data as ApiLessonWidget;
        return {
          index,
          name,
          type,
          data: widgetData.data,
        } as CourseWidget<"guide">;
      }
    }

    if (type === "video") {
      const widgetData = data as ApiLessonWidget;
      return {
        index,
        name,
        type,
        data: widgetData.data,
      } as CourseWidget<"video">;
    }

    if (type === "interactive") {
      const widgetData = data as ApiLessonWidget;
      return {
        index,
        name,
        type,
        data: widgetData.data,
      } as CourseWidget<"interactive">;
    }

    return undefined;
  };

  return {
    data: parse(data),
    isLoading,
    error,
    refresh: () => mutate(undefined, { revalidate: true }),
  };
};

export { useCourseWidgetModel };
