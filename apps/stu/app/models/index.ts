// 导出 comments 模块
export {
  useAddComment,
  useCommentsList,
  useDeleteComment,
  useLikeComment,
  useReplyComment,
  useSubCommentsList,
} from "./comments";

// 导出 comments 类型
export type {
  AddCommentPayload,
  Comment,
  CommentsListData,
  DeleteCommentPayload,
  ExtraInfo,
  GetCommentsParams,
  GetSubCommentsParams,
  LikeCommentPayload,
  ReferenceCoordinate,
  ReferencePosition,
  ReplyCommentPayload,
} from "./comments";

// 导出 comments Schema
export {
  ApiCommentSchema,
  ApiCommentsListDataSchema,
  ApiCommonResponseSchema,
  ApiGetCommentsResponseSchema,
  ApiReferenceCoordinateSchema,
  ApiReferencePositionSchema,
} from "./comments";
// 🔥 错题本 Schema 已删除，不再需要运行时校验

// 导出 interactive-explanation 类型
export type {
  InteractiveExplanationRecord,
  ReportInteractiveExplanationPayload,
  ReportResult,
  StepByStepGuideItem,
} from "../../../../packages/core/src/interactive-explanation/model";
