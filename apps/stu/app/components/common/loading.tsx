import loadingGif from "@/public/images/loading.gif";
import { cn } from "@repo/ui/lib/utils";
import Image from "next/image";
import { FC } from "react";
export const Loading: FC<{ className?: string }> = ({ className }) => {
  return (
    <div
      className={cn(
        "flex h-full w-full items-center justify-center",
        className
      )}
    >
      <Image
        priority
        className="size-15"
        src={loadingGif}
        unoptimized
        alt="loading"
        width={60}
        height={60}
      />
    </div>
  );
};
