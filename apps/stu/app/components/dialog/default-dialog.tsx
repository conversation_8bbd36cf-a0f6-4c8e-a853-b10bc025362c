import { useTap } from "@/app/hooks/use-tap";
import { StudyType } from "@repo/core/enums";
import Button from "@repo/ui/components/press-button";
import { cn } from "@repo/ui/lib/utils";
import { FC, ReactNode } from "react";
import ReactDOM from "react-dom";

/**
 * 对话框组件
 * @param title 对话框标题
 * @param children 对话框内容
 * @param open 是否显示对话框
 * @param onClose 关闭对话框的回调函数
 * @param buttons 对话框按钮配置
 * @param buttons.text 按钮文本
 * @param buttons.onClick 按钮点击回调
 * @param buttons.type 按钮类型,'primary'为主按钮,'default'为次按钮
 */

export interface DialogProps {
  title?: string;
  subtitle?: string;
  children?: ReactNode;
  open?: boolean;
  onClose?: () => void;
  buttons?: {
    text: string;
    onClick: () => void;
    color?: "orange" | "red" | "green" | "gray" | "white" | "study-theme";
    disabled?: boolean;
    className?: string;
    studyType?: StudyType;
  }[];
  studyType?: StudyType;
}

export const DialogView: FC<DialogProps> = ({
  title,
  subtitle,
  children,
  open = false,
  onClose,
  buttons = [],
  studyType,
}) => {
  const tapsHandlers = useTap(onClose);

  if (!open) return null;

  return ReactDOM.createPortal(
    <div className="flex font-resource-han-rounded inset-0 z-50 dialog-view fixed items-center justify-center">
      <div
        className="bg-[rgba(0,0,0,0.5)] inset-0 dialog-backdrop fixed"
        onClick={onClose}
      />
      <div className="bg-white rounded-2xl shadow-lg w-80 z-10 dialog-content relative">
        {title && (
          <div className="font-bold text-center text-lg px-6 pt-9 pb-2 text-[#1F232B] dialog-title">
            {title}
          </div>
        )}

        {subtitle && (
          <div className="text-center text-sm px-6 pb-4 text-[#666666] dialog-subtitle">
            {subtitle}
          </div>
        )}

        {children && (
          <div className="text-center text-sm px-6 pb-4 text-[#666666] dialog-body">
            {children}
          </div>
        )}

        {buttons.length > 0 && (
          <div className="flex px-6 pt-5 pb-6 gap-3 dialog-buttons">
            {buttons.map((button, index) => (
              <Button
                key={index}
                studyType={button.studyType || studyType}
                color={button.color || "orange"}
                className={cn("flex-1", button.className)}
                onClick={button.onClick}
                disabled={button.disabled}
                source="dialog"
              >
                {button.text}
              </Button>
            ))}
          </div>
        )}
      </div>
    </div>,
    document.body
  );
};
