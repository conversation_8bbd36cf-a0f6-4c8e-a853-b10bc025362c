"use client";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Checkbox } from "@/app/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/app/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table";
import useEnum from "@/app/hooks/useEnumManager/useEnumManager";
import { get as adminGet, post as adminPost } from "@/lib/admin-fetcher";
import { get, post } from "@/lib/course-center-fetcher";
import emitter from "@/lib/emitter";
import { getLoginUrl, saveTokenFormUrl } from "@/lib/login-helper";
import { cn } from "@/lib/utils";
import UserImg from "@/public/user.svg";
import { useEffect } from "@preact-signals/safe-react/react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@repo/ui/components/tooltip";
import { useRequest } from "ahooks";
import { useRef, useState } from "react";
import { Toaster, toast } from "sonner";
import BatchCreation from "./_components/BatchCreation";
import CreationModal from "./_components/Creation";
import DetailModal from "./_components/DetailModal";
import SearchForm from "./_components/search-form";

const isProd = process.env.NEXT_PUBLIC_ENV === "production";

const statusEnum: Record<number, string> = {
  1: "未上架",
  2: "已上架",
  3: "修改待上架",

  // 1: "待上架",
  // 2: "已发布",
  // 3: "待发布",
};

const getCountdownTime = (serverTime?: number) => {
  if (!serverTime) return 10 * 1000;

  const now = new Date(serverTime * 1000);
  const currentHour = now.getHours();

  // 计算距离下次状态变更的时间（秒）
  let nextChangeTime: Date;

  if (currentHour >= 0 && currentHour < 6) {
    // 当前在0-6点，下次变更是6点
    nextChangeTime = new Date(now);
    nextChangeTime.setHours(6, 0, 0, 0);
  } else if (currentHour >= 6 && currentHour < 12) {
    // 当前在6-12点，下次变更是12点
    nextChangeTime = new Date(now);
    nextChangeTime.setHours(12, 0, 0, 0);
  } else if (currentHour >= 12 && currentHour < 13) {
    // 当前在12-13点，下次变更是13点
    nextChangeTime = new Date(now);
    nextChangeTime.setHours(13, 0, 0, 0);
  } else if (currentHour >= 13 && currentHour < 22) {
    // 当前在13-22点，下次变更是22点
    nextChangeTime = new Date(now);
    nextChangeTime.setHours(22, 0, 0, 0);
  } else {
    // 当前在22-24点，下次变更是次日6点
    nextChangeTime = new Date(now);
    nextChangeTime.setDate(nextChangeTime.getDate() + 1);
    nextChangeTime.setHours(6, 0, 0, 0);
  }

  const timeDiff = Math.floor(
    (nextChangeTime.getTime() - now.getTime()) / 1000
  );

  // 根据时间差返回相应的倒计时时间
  if (timeDiff > 3600) {
    // 超过1小时，返回1小时
    return 3600 * 1000;
  } else if (timeDiff > 600) {
    // 超过10分钟但不足1小时，返回10分钟
    return 600 * 1000;
  } else if (timeDiff > 60) {
    // 超过1分钟但不足10分钟，返回1分钟
    return 60 * 1000;
  } else {
    // 不足1分钟，返回10秒
    return 10 * 1000;
  }
};

const getIsValidTime = (serverTime?: number) => {
  if (!serverTime) return false;
  const now = new Date(serverTime * 1000);
  const hours = now.getHours();
  // 0点-6点之间，允许上架
  if (hours >= 0 && hours < 6) return true;
  // 12点-13点之间，允许上架
  if (hours === 12) return true;
  // 22点-24点之间，允许上架
  if (hours >= 22) return true;

  return false;
};

const hiddenTime: number | null = null;
function FlowDemo() {
  saveTokenFormUrl();

  const { enums, getByValue } = useEnum();
  const [open, setOpen] = useState(false);
  const [batchOpen, setBatchOpen] = useState(false);
  const [editStates, setEditStates] = useState<Record<string, boolean>>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isValidTime, setIsValidTime] = useState<boolean>(
    isProd ? getIsValidTime() : true
  );
  const [modalConfig, setModalConfig] = useState<{
    lessonId: string;
    modal: string;
    open: boolean;
  }>({
    lessonId: "",
    modal: "create",
    open: false,
  });

  const { data: userInfo, run: runUserInfo }: any = useRequest(() =>
    adminGet(
      "/api/v1/user/detail_by_token",
      {
        query: {
          platformId: "2",
        },
      },
      {
        isOriginData: true,
      }
    )
  );
  const [searchParams, setSearchParams] = useState({});
  const { data = { list: [], total: 0 }, run }: any = useRequest(
    (query = {}) => {
      const queryTemp = {
        ...query,
        page: query.page ?? currentPage,
        pageSize,
      };
      return get("/api/v1/lesson/list", { query: queryTemp });
    },

    {
      refreshDeps: [currentPage, pageSize],
    }
  );

  const resetPage = () => {
    setCurrentPage(1);
    setPageSize(10);
  };

  const [selected, setSelected] = useState<{ id: string; status: number }[]>(
    []
  );
  const [isBatchUpdating, setIsBatchUpdating] = useState(false);

  // 全选/取消全选
  const allChecked = selected.length === data?.list?.length;
  const isIndeterminate = selected.length > 0 && !allChecked;

  const handleCheckAll = (checked: boolean) => {
    setSelected(
      checked
        ? data?.list?.map((row: any) => ({
            id: row.lessonId,
            status: row.status,
          }))
        : []
    );
  };

  const handleCheckRow = (id: string, checked: boolean, status: number) => {
    setSelected((prev) =>
      checked
        ? [...prev, { id, status }]
        : prev.filter((item) => item.id !== id)
    );
  };

  const handleBatchCreate = () => {
    emitter.emit("batchCreateCourse");
  };

  const onUpdate = async (lessonIdTemp: string | Array<string>) => {
    try {
      // 如果是批量操作，设置loading状态
      if (Array.isArray(lessonIdTemp)) {
        setIsBatchUpdating(true);
      }

      const res: any = await post("/api/v1/lesson/publish", {
        arg: {
          lessonIds: Array.isArray(lessonIdTemp)
            ? lessonIdTemp
            : [lessonIdTemp],
        },
      });

      if (res.faild && res.faild.length > 0) {
        let msg = "操作失败！";
        res.faild.forEach((element: any) => {
          msg += `<br />id为${element.lessonId}的课程： ${element.errorMsg}`;
        });
        toast.error(<div dangerouslySetInnerHTML={{ __html: msg }} />);
      } else {
        toast.success("操作完成！");
      }
      run(searchParams);
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      // 重置loading状态
      if (Array.isArray(lessonIdTemp)) {
        setIsBatchUpdating(false);
      }
    }
  };

  const handleBatchPublish = () => {
    if (selected.length === 0) {
      toast.error("请选择要操作的课程");
      return;
    }
    if (isBatchUpdating) {
      return; // 防止重复点击
    }
    // 检测是否有已上架的课
    const updateIds = selected
      .filter((item) => item.status !== 1)
      .map((item) => item.id);
    if (updateIds.length > 0) {
      toast.error(`${updateIds.join(",")} 已上架，请选择未上架的课程`);
      return;
    }
    onUpdate(selected.map((item) => item.id));
  };
  const handleBatchUpdate = () => {
    if (!isValidTime) return;
    if (selected.length === 0) {
      toast.error("请选择要操作的课程");
      return;
    }
    if (isBatchUpdating) {
      return; // 防止重复点击
    }
    onUpdate(selected.map((item) => item.id));
  };

  const handleDownload = () => {
    get("/api/v1/lesson/export", {
      query: {
        ...searchParams,
        page: currentPage.toString(),
        pageSize: pageSize.toString(),
      },
    }).then((res: any) => {
      if (window) {
        window?.open(res.url);
      }
    });
  };

  const handleOpenModal = (lessonId: string, modal: string) => {
    setModalConfig({
      lessonId,
      modal,
      open: true,
    });
  };

  const handleCloseModal = () => {
    setModalConfig((prev) => ({
      ...prev,
      open: false,
    }));
  };

  const logout = async () => {
    await adminPost("/api/v1/user/logout", {});
    const loginUrl = getLoginUrl();
    localStorage.clear();
    location.href = loginUrl;
  };

  useEffect(() => {
    const handleRefresh = () => {
      run();
    };

    emitter.on("refresh", handleRefresh);

    // 清理函数
    return () => {
      emitter.off("refresh", handleRefresh);
    };
  }, [run]);

  const timer = useRef<NodeJS.Timeout | null>(null);
  const startCountdown = (serverTime: number) => {
    if (!isProd) return;
    console.log("serverTime", serverTime);
    const isValidTime = getIsValidTime(serverTime);
    const countdownTime = getCountdownTime(serverTime);
    setIsValidTime(isValidTime);
    timer.current = setTimeout(() => {
      startCountdown(serverTime + countdownTime);
    }, countdownTime);
  };
  useEffect(() => {
    startCountdown(userInfo?.response_time);

    return () => {
      if (timer.current) {
        clearTimeout(timer.current);
      }
    };
  }, [userInfo]);

  useEffect(() => {
    const cb = () => {
      console.log("document.visibilityState", document.visibilityState);
      if (isProd) {
        if (document.visibilityState === "visible") {
          runUserInfo();
        }
      }
    };
    document.addEventListener("visibilitychange", cb);
    return () => {
      document.removeEventListener("visibilitychange", cb);
    };
  }, []);

  return (
    <>
      <div className="flex flex-col w-screen">
        <div className="bg-white flex p-4 items-center justify-between">
          <div className="flex font-bold text-xl gap-4 items-center">
            AI课-课程管理
          </div>
          <div className="flex mr-10 items-center">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="cursor-pointer flex gap-2 items-center">
                  <UserImg width={20} height={20} alt="user" />
                  {userInfo?.data?.userName || ""}
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="cursor-pointer w-auto"
                align="start"
              >
                <DropdownMenuItem onClick={logout}>退出登录</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <div className="bg-gray-100 flex-1 p-10">
          <SearchForm
            syncRequest={run}
            resetPage={resetPage}
            setSearchParams={setSearchParams}
          />

          <div className="flex mt-10 items-center justify-between">
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handleBatchPublish}
                disabled={isBatchUpdating}
              >
                {isBatchUpdating ? "上架中..." : "批量上架"}
              </Button>
              <Button
                variant="outline"
                onClick={handleBatchUpdate}
                disabled={isBatchUpdating || !isValidTime}
              >
                {isBatchUpdating ? "更新中..." : "批量更新"}
              </Button>

              <Dialog open={batchOpen} onOpenChange={setBatchOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline">批量创建课程</Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>批量创建课程</DialogTitle>
                    <DialogDescription></DialogDescription>
                  </DialogHeader>
                  <BatchCreation setOpen={setBatchOpen} />
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setBatchOpen(false)}
                    >
                      取消
                    </Button>
                    <Button className="bg-blue-500" onClick={handleBatchCreate}>
                      完成
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Button
                className="bg-indigo-400"
                onClick={() => handleOpenModal("", "create")}
              >
                +创建课程
              </Button>
            </div>

            <Button variant="outline" onClick={handleDownload}>
              下载表格
            </Button>
          </div>
          <div className="bg-white rounded-md mt-2 p-4">
            {data?.list?.length > 0 ? (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>
                        <Checkbox
                          className="border-gray-300"
                          checked={allChecked}
                          // indeterminate={isIndeterminate}
                          onCheckedChange={handleCheckAll}
                        />
                      </TableHead>
                      <TableHead>ID</TableHead>
                      <TableHead>名称</TableHead>
                      <TableHead>学段</TableHead>
                      <TableHead>学科</TableHead>
                      <TableHead>课程类型</TableHead>
                      <TableHead>业务树知识点</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead>创建人</TableHead>
                      <TableHead>上架状态</TableHead>
                      <TableHead>上架时间</TableHead>
                      <TableHead>上架人</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data?.list?.map((row: any, idx: number) => (
                      <TableRow key={idx}>
                        <TableCell>
                          <Checkbox
                            checked={selected.some(
                              (c) => c.id === row.lessonId
                            )}
                            className="border-gray-300"
                            onCheckedChange={(checked) =>
                              handleCheckRow(
                                row.lessonId,
                                !!checked,
                                row.status
                              )
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <span className="cursor-pointer text-blue-600 hover:underline">
                            {row.lessonId}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="cursor-pointer text-blue-600 hover:underline">
                            {row.lessonName}
                          </span>
                        </TableCell>
                        <TableCell>
                          {getByValue(enums.phaseList, row.phase).label}
                        </TableCell>
                        <TableCell>
                          {getByValue(enums.subjectList, row.subject).label}
                        </TableCell>
                        <TableCell>
                          {
                            getByValue(enums.lessonTypeList, row.lessonType)
                              .label
                          }
                        </TableCell>
                        <TableCell>
                          {row.bizTreeNodeList.map((item: any) => (
                            <div
                              className="cursor-pointer text-blue-600 hover:underline"
                              key={item.bizTreeNodeId}
                            >
                              {item.bizTreeNodeName}
                            </div>
                          ))}
                        </TableCell>
                        <TableCell>{row.createTime}</TableCell>
                        <TableCell>{row.creator}</TableCell>
                        <TableCell>
                          <span className="rounded bg-blue-100 py-1 px-2 text-blue-600">
                            {statusEnum[row.status]}
                          </span>
                        </TableCell>
                        <TableCell>{row.publishTime}</TableCell>
                        <TableCell>{row.publisher}</TableCell>
                        <TableCell>
                          <span
                            className="cursor-pointer mr-2 text-blue-600 hover:underline"
                            onClick={() =>
                              handleOpenModal(row.lessonId, "edit")
                            }
                          >
                            编辑
                          </span>
                          <span
                            className="cursor-pointer mr-2 text-blue-600 hover:underline"
                            onClick={() =>
                              handleOpenModal(row.lessonId, "detail")
                            }
                          >
                            查看详情
                          </span>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span
                                className={cn(
                                  "mr-2 cursor-pointer text-blue-600 hover:underline",
                                  row.phase !== 4 &&
                                    !isValidTime &&
                                    row.status !== 1 &&
                                    "cursor-not-allowed text-gray-500"
                                )}
                                onClick={() => {
                                  // 内测学段和未上架的课不受时间限制
                                  if (
                                    row.phase !== 4 &&
                                    !isValidTime &&
                                    row.status !== 1
                                  )
                                    return;
                                  onUpdate(row.lessonId);
                                }}
                              >
                                {row.status === 1 ? "上架" : "更新"}
                              </span>
                            </TooltipTrigger>
                            {row.phase !== 4 &&
                              !isValidTime &&
                              row.status !== 1 && (
                                <TooltipContent
                                  hideArrow={true}
                                  className="bg-red-500"
                                >
                                  当前时间不允许更新
                                </TooltipContent>
                              )}
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                <div className="flex mt-4 justify-end">
                  <div className="flex gap-2 items-center">
                    <span className="text-sm text-gray-500">
                      每页显示
                      <select
                        className="border rounded mx-2 py-1 px-2"
                        value={pageSize}
                        onChange={(e) => {
                          setPageSize(Number(e.target.value));
                          setCurrentPage(1);
                          run(searchParams);
                        }}
                      >
                        <option value={10}>10</option>
                        <option value={20}>20</option>
                        <option value={50}>50</option>
                      </select>
                      条
                    </span>
                    <span className="text-sm text-gray-500">
                      第 {currentPage} 页，共 {Math.ceil(data.total / pageSize)}{" "}
                      页
                    </span>
                    <span className="text-sm text-gray-500">
                      共 {data.total} 条
                    </span>
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={currentPage === 1}
                        onClick={() => {
                          setCurrentPage(currentPage - 1);
                          run(searchParams);
                        }}
                      >
                        上一页
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={currentPage * pageSize >= data.total}
                        onClick={() => {
                          setCurrentPage(currentPage + 1);
                          run(searchParams);
                        }}
                      >
                        下一页
                      </Button>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center text-gray-500">暂无数据</div>
            )}
          </div>
        </div>

        <CreationModal
          lessonId={modalConfig.lessonId}
          modal={modalConfig.modal}
          open={modalConfig.open && modalConfig.modal !== "detail"}
          onClose={handleCloseModal}
          refresh={() => run(searchParams)}
        />
        <DetailModal
          lessonId={modalConfig.lessonId}
          open={modalConfig.open && modalConfig.modal === "detail"}
          onClose={handleCloseModal}
        />
      </div>
      <Toaster position="top-center" />
    </>
  );
}

export default FlowDemo;
