import { But<PERSON> } from "@/app/components/ui/button";
import {
  Sheet,
  She<PERSON><PERSON>lose,
  She<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/app/components/ui/sheet";
import emitter from "@/lib/emitter";
import { get } from "@/lib/fetcher";
import { useRequest } from "ahooks";
import { useEffect, useMemo, useState } from "react";
import { InteractiveView } from "./interactive/interactive-view";
import "./style.scss";

interface ExerciseProps {
  lessonId: string;
  open: boolean;
  setAllValue?: (value: string) => void;
  onClose: () => void;
  widgetIndex: string;
  refresh?: () => void;
  setPcurrentWidget?: (value: string) => void;
}

const Exercise = (props: ExerciseProps) => {
  const { data, loading } = useRequest<any, [string]>(
    () =>
      get("/api/v1/lesson_widget", {
        query: { lessonId: props.lessonId },
      }),
    {
      ready: !!props.lessonId && props.open,
    }
  );
  const currentWidget = useMemo(() => {
    if (data) {
      return data.widgetList[props.widgetIndex];
    }
    return null;
  }, [data, props.widgetIndex]);
  const currentInteractive = useMemo(
    () => currentWidget?.interactive,
    [currentWidget]
  );

  useEffect(() => {
    if (props.setAllValue && currentWidget) {
      props.setAllValue(currentWidget);
    }
  }, [currentWidget]);
  if (loading) {
    return <div>loading...</div>;
  }

  if (!data) {
    return <div>请求参数错误，请刷新重试</div>;
  }

  return (
    <div className="h-[600px] w-[960px] overflow-y-auto px-4">
      <InteractiveView
        active={true}
        index={data.index}
        url={currentInteractive.url}
        type={currentInteractive.typeName}
        onReport={(e) => {
          console.log(e);
        }}
      />
    </div>
  );
};

const ExerciseSheet = (props: ExerciseProps) => {
  const [allValue, setAllValue] = useState<any>({});

  const editAll = async () => {
    //   emitter.emit("editAll", {
    //     index: props.widgetIndex,
    //     exercise: ids,
    //   });

    emitter.emit("openEditModal", {
      ...props,
      ...allValue,
      widgetType: "interactive",
      previous: Number(props.widgetIndex) - 1,
    });
  };

  const downLoadFile = () => {
    if (allValue?.interactive?.url) {
      fetch(allValue?.interactive?.url)
        .then((res) => res.blob())
        .then((blob) => {
          const url = URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = "";
          a.click();
          URL.revokeObjectURL(url);
        });
    }
  };

  return (
    <Sheet open={props.open} onOpenChange={props.onClose}>
      <SheetContent className="w-[1000px] !max-w-none gap-2 bg-gray-50">
        <SheetHeader className="gap-0 pb-0">
          <div className="flex items-center justify-between">
            <SheetTitle>互动预览</SheetTitle>
          </div>
          <SheetDescription></SheetDescription>
        </SheetHeader>
        <Exercise {...props} setAllValue={setAllValue} />
        <SheetFooter>
          <SheetClose asChild>
            <div className="flex items-center justify-end gap-2">
              <Button onClick={downLoadFile}>下载源文件</Button>
              <Button onClick={editAll}>编辑互动组件</Button>
            </div>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default ExerciseSheet;
