import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import {
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  SheetD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/app/components/ui/sheet";
import { Textarea } from "@/app/components/ui/textarea";
import emitter from "@/lib/emitter";
import { get, post } from "@/lib/fetcher";
import { transformQuestionData } from "@/lib/utils";
import DeleteImg from "@/public/delete.svg";
import { QuestionItem } from "@repo/core/views/tch-question-view";
import { useRequest } from "ahooks";
import { Modal } from "antd";
import { useEffect, useRef, useState } from "react";
import "./style.scss";

interface InteractiveProps {
  lessonId: string;
  open: boolean;
  setAllValue?: (value: string) => void;
  onClose: () => void;
  widgetIndex: string;
  refresh?: () => void;
}

const Interactive = (props: InteractiveProps) => {
  const { data: qsDetails, refresh } = useRequest<any, [string]>(
    () =>
      get(`/api/v1/lesson_widget/question/get`, {
        query: { lessonId: props.lessonId, widgetIndex: props.widgetIndex },
      }),
    {
      ready: !!props.lessonId && props.open,
      onSuccess: (res) => {
        const all = res.questionList.map((item: Array<any>) =>
          item
            .filter((it: any) => it)
            .map((it: any) => transformQuestionData(it))
        );
        const ids = all
          .map((item: any) =>
            item
              .filter((q: any) => q)
              .map((q: any) => q.questionId)
              .join("｜")
          )
          .join("\n");

        if (props.setAllValue) {
          props.setAllValue(ids);
        }
        return res;
      },
    }
  );

  const updateExercise = async (exercise: string) => {
    const res = await post("/api/v1/lesson_widget/update/exercise", {
      arg: {
        lessonId: Number(props.lessonId),
        widgetIndex: props.widgetIndex,
        exercise,
      },
    });
    if (props.refresh) {
      props.refresh();
    }
    return res;
  };

  let questionDatas = null;
  const questionDatasRef = useRef<Array<any>>(null);
  if (qsDetails) {
    questionDatas = qsDetails.questionList.map((item: any) =>
      item.filter((it: any) => it).map((it: any) => transformQuestionData(it))
    );
    questionDatasRef.current = questionDatas;
  }

  const [modal, contextHolder] = Modal.useModal();

  const handleAction = async (currentItem: any, action: string) => {
    if (action === "up") {
      const index = questionDatas.findIndex(
        (item: any) => item[0].questionId === currentItem[0].questionId
      );
      if (index > 0) {
        const temp = questionDatas[index - 1];
        questionDatas[index - 1] = currentItem;
        questionDatas[index] = temp;
      }
      const ids = questionDatas
        .map((item: any) =>
          item
            .filter((q: any) => q)
            .map((q: any) => q.questionId)
            .join("｜")
        )
        .join("\n");
      await updateExercise(ids);
      refresh();
    } else if (action === "down") {
      const index = questionDatas.findIndex(
        (item: any) => item[0].questionId === currentItem[0].questionId
      );
      if (index < questionDatas.length - 1) {
        const temp = questionDatas[index + 1];
        questionDatas[index + 1] = currentItem;
        questionDatas[index] = temp;
      }
      const ids = questionDatas
        .map((item: any) =>
          item
            .filter((q: any) => q)
            .map((q: any) => q.questionId)
            .join("｜")
        )
        .join("\n");
      await updateExercise(ids);
      refresh();
    } else if (action === "rm") {
      const res = await modal.confirm({
        title: "确认删除？",
        okText: "确定",
        cancelText: "取消",
      });
      if (res) {
        const index = questionDatas.findIndex(
          (item: any) => item[0].questionId === currentItem[0].questionId
        );
        if (index > -1) {
          questionDatas.splice(index, 1);
        }
        const ids = questionDatas
          .map((item: any) =>
            item
              .filter((q: any) => q)
              .map((q: any) => q.questionId)
              .join("｜")
          )
          .join("\n");
        await updateExercise(ids);
        refresh();
        if (props.refresh) {
          props.refresh();
        }
      }
    } else if (action === "repleace") {
      // itemGroup.openReplace = true;
      const ids = questionDatas
        .map((item: any) =>
          item
            .filter((q: any) => q)
            .map((q: any) => q.questionId)
            .join("｜")
        )
        .join("\n");
      const index = questionDatas.findIndex(
        (item: any) => item[0].questionId === currentItem[0].questionId
      );
      emitter.emit("repleaceQs", {
        ids,
        index,
        lessonId: Number(props.lessonId),
        widgetIndex: props.widgetIndex,
      });
    }
  };
  useEffect(() => {
    const handleUpdateExercise = async (value: string) => {
      await updateExercise(value);
      refresh();
    };

    const handleAddExercise = async (value: string) => {
      let finalValue = value;
      if (questionDatasRef.current) {
        const ids = questionDatasRef.current
          .map((item: any) =>
            item
              .filter((q: any) => q)
              .map((q: any) => q.questionId)
              .join("｜")
          )
          .join("\n");
        finalValue = ids + "\n" + value;
      }

      await updateExercise(finalValue);
      refresh();
    };

    const handleRefresh = () => {
      refresh();
    };

    // @ts-ignore
    emitter.on("updateExercise", handleUpdateExercise);
    // @ts-ignore
    emitter.on("addExercise", handleAddExercise);
    emitter.on("refresh", handleRefresh);

    // 清理函数
    return () => {
      // @ts-ignore
      emitter.off("updateExercise", handleUpdateExercise);
      // @ts-ignore
      emitter.off("addExercise", handleAddExercise);
      emitter.off("refresh", handleRefresh);
    };
  }, [refresh]);

  return (
    <div className="overflow-y-auto px-4">
      {questionDatas &&
        questionDatas.map(
          (itemGroup: Array<any>, index: number) =>
            itemGroup.some((it) => it) && (
              <div
                key={index}
                className="group relative rounded-lg"
                style={index === 0 ? { marginTop: "0px" } : {}}
              >
                <div className="z-2 absolute right-[10px] top-[6px] flex items-center gap-2 rounded-lg bg-white p-1 px-2 text-sm opacity-0 shadow-md group-hover:opacity-100">
                  {index > 0 && (
                    <div
                      className="cursor-pointer text-blue-500"
                      onClick={() => handleAction(itemGroup, "up")}
                    >
                      上移
                    </div>
                  )}
                  {index < questionDatas.length - 1 && (
                    <div
                      className="cursor-pointer text-blue-500"
                      onClick={() => handleAction(itemGroup, "down")}
                    >
                      下移
                    </div>
                  )}

                  <div
                    className="cursor-pointer text-blue-500"
                    onClick={() => handleAction(itemGroup, "rm")}
                  >
                    删除
                  </div>

                  <div
                    className="cursor-pointer text-blue-500"
                    onClick={() => handleAction(itemGroup, "repleace")}
                  >
                    换题
                  </div>
                </div>
                {itemGroup.map((item: any, idx: number) => {
                  const customIndex =
                    itemGroup.length > 1
                      ? `${index + 1} - ${idx + 1}`
                      : index + 1;
                  return (
                    <div
                      key={idx}
                      className="custom-question-item mt-1"
                      style={{ zoom: 0.88 }}
                    >
                      <QuestionItem
                        qaContent={item}
                        customIndex={String(customIndex)}
                        hasFooterButton={false}
                        key={index + "-" + idx}
                      />
                    </div>
                  );
                })}
              </div>
            )
        )}
      {contextHolder}
    </div>
  );
};

const InteractiveSheet = (props: InteractiveProps) => {
  const [allValue, setAllValue] = useState("");
  const [openAll, setOpenAll] = useState(false);

  const updateExercise = async () => {
    emitter.emit("updateExercise", allValue);
  };

  const [openAdd, setOpenAdd] = useState(false);
  const [addValue, setAddValue] = useState("");
  const handleAdd = async (value: string) => {
    // setOpenAdd(false);
    emitter.emit("addExercise", value);
  };

  // const { data: qsDetails, refresh } = useRequest<any, [string]>(
  //   () =>
  //     get(`/api/v1/lesson_widget/question/get`, {
  //       query: { lessonId: props.lessonId, widgetIndex: props.widgetIndex },
  //     }),
  //   {
  //     ready: !!props.lessonId && props.open,
  //     onSuccess: (res) => {
  //       const all = res.questionList.map((item: Array<any>) =>
  //         item
  //           .filter((it: any) => it)
  //           .map((it: any) => transformQuestionData(it))
  //       );
  //       const ids = all
  //         .map((item: any) =>
  //           item
  //             .filter((q: any) => q)
  //             .map((q: any) => q.questionId)
  //             .join("｜")
  //         )
  //         .join("\n");

  //       if (props.setAllValue) {
  //         props.setAllValue(ids);
  //       }
  //       return res;
  //     },
  //   }
  // );

  const editAll = async () => {
    const { questionList }: any = await get(
      `/api/v1/lesson_widget/question/get`,
      {
        query: { lessonId: props.lessonId, widgetIndex: props.widgetIndex },
      }
    );

    const ids = questionList
      .map((item: any) =>
        item
          .filter((q: any) => q)
          .map((q: any) => q.questionId)
          .join("｜")
      )
      .join("\n");
    emitter.emit("editAll", {
      index: props.widgetIndex,
      exercise: ids,
    });
  };

  const [modal, contextHolder] = Modal.useModal();
  const clearAll = async () => {
    const res = await modal.confirm({
      title: "确认清空？",
      okText: "确定",
      cancelText: "取消",
    });
    if (res) {
      await emitter.emit("updateExercise", "");
      setTimeout(() => {
        props.onClose();
      });
    }
  };

  return (
    <Sheet open={props.open} onOpenChange={props.onClose}>
      <SheetContent className="w-2/3 !max-w-none gap-2 bg-gray-50">
        <SheetHeader className="gap-0 pb-0">
          <div className="flex items-center justify-between">
            <SheetTitle>预览编辑练习</SheetTitle>
            <div className="mr-10 flex items-center gap-2">
              {openAdd && (
                <div className="flex items-center">
                  <Input
                    placeholder="请在此输入..."
                    onInput={(event: any) => setAddValue(event.target.value)}
                    value={addValue}
                  />
                  <Button onClick={() => handleAdd(addValue)}>添加</Button>
                </div>
              )}
              {/* {openAdd ? (
                <Button onClick={() => setOpenAdd(false)}>
                  隐藏【添加题目】
                </Button>
              ) : (
                <Button onClick={() => setOpenAdd(true)}>添加题目</Button>
              )} */}
            </div>
            <div>{contextHolder}</div>
          </div>
          <SheetDescription></SheetDescription>
        </SheetHeader>
        <Interactive {...props} setAllValue={setAllValue} />
        <SheetFooter>
          <SheetClose asChild>
            <>
              {openAll && (
                <div className="flex items-center gap-2">
                  <Textarea
                    placeholder="请在此输入..."
                    onInput={(event: any) => setAllValue(event.target.value)}
                    value={allValue}
                  />
                  <Button onClick={updateExercise}>更新</Button>
                </div>
              )}
              <div className="flex items-center justify-end gap-2">
                <Button variant="outline" onClick={clearAll}>
                  <DeleteImg width={20} height={20} />
                  清空
                </Button>
                {/* {openAll ? (
                  <Button variant="outline" onClick={() => setOpenAll(false)}>
                    取消【编辑全部题目】
                  </Button>
                ) : (
                  <Button variant="outline" onClick={() => setOpenAll(true)}>
                    编辑全部题目
                  </Button>
                )} */}
                <Button onClick={editAll}>编辑全部题目</Button>
              </div>
            </>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

export default InteractiveSheet;
