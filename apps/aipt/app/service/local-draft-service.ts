import {
  getStorageItem,
  removeStorageItem
} from "@repo/lib/utils/local-storage";
import localforage from "localforage";
localforage.config({
  name: "aipt",
  driver: localforage.INDEXEDDB,
});

export class LocalDraftService {
  private guideKey: string;

  constructor({
    guideWidgetSetId,
    guideWidgetId,
    key = "guide",
  }: {
    guideWidgetSetId: string;
    guideWidgetId: string;
    key?: string;
  }) {
    this.guideKey = `${key}:${guideWidgetSetId}-${guideWidgetId}`;
  }

  async load<T>(name: string, defaultValue?: T) {
    const key = `${this.guideKey}-${name}`;
    console.log('load', key);

    // 兼容之前存在localStorage中的数据
    const data = getStorageItem<T>(key, defaultValue as T);
    if (data && data !== defaultValue) {
      // 1、存到indexdb中
      await this.save(name, data);
      // 2、将localStorage中数据删除以便后续都用indexdb存储
      removeStorageItem(key);
      return data;
    }

    return localforage.getItem(key)
      .then((value) => JSON.parse(value as string))
      .catch((error) => {
        console.error(`Error reading indexDB key "${key}":`, error);
        return defaultValue;
      });
    // return getStorageItem<T>(`${this.guideKey}-${name}`, defaultValue as T);
  }

  save<T>(name: string, content: T) {
    const key = `${this.guideKey}-${name}`;
    try {
      return localforage.setItem(`${this.guideKey}-${name}`, JSON.stringify(content))
    } catch (error) {
      console.error(`Error writing to indexDB key "${key}":`, error);
      return false;
    }
    // setStorageItem<T>(`${this.guideKey}-${name}`, content);
  }

  clear(name: string) {
    return localforage.removeItem(`${this.guideKey}-${name}`);
    // removeStorageItem(`${this.guideKey}-${name}`);
  }

  clearAll() {
    return localforage.clear();
    // clearStorage();
  }
}
