"use client";

import { post } from "@/app/utils/fetcher";
import { GuideTaskType } from "@/types/base";
import {
  ReadonlySignal,
  Signal,
  signal,
  useComputed,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useLayoutEffect,
} from "react";
import useSWRMutation from "swr/mutation";
import { useGuideContext } from "../../context/guide-context";

type ServerType = "transcript" | "boardscript";

export interface MdEditorContextType {
  serverType: ServerType;
  content: Signal<string>;
  reset: () => void;
  save: () => void;
  isSaving: boolean;
  contentChanged: ReadonlySignal<boolean>;
  isGenerating: Signal<boolean>;
}

const MdEditorContext = createContext<MdEditorContextType>({
  serverType: "transcript",
  content: signal(""),
  reset: () => {},
  save: () => {},
  isSaving: false,
  contentChanged: signal(false),
  isGenerating: signal(false),
});

const useMdEditorContext = () => useContext(MdEditorContext);

interface MdEditorProviderProps {
  children: React.ReactNode;
  serverType: ServerType;
}

const MdEditorProvider = ({ children, serverType }: MdEditorProviderProps) => {
  const {
    guide,
    localDraft,
    hasPanelUnSavedContent,
    isGenerating: isGeneratingGuide,
  } = useGuideContext();

  const { trigger: saveTrigger, isMutating: isSaving } = useSWRMutation(
    `/api/v1/guideWidget/save/${serverType}`,
    post
  );
  const initialContent = useSignal(guide[serverType]);

  useEffect(() => {
    initialContent.value = guide[serverType];
  }, [serverType, guide, initialContent]);

  // const draftContent = useMemo(async () => {
  //   return await localDraft.load<string>(serverType, "");
  // }, [serverType, localDraft]);
  // console.log('draftContent', draftContent);

  const content = useSignal(initialContent.value);

  // 从本地取一次数据
  useLayoutEffect(() => {
    localDraft.load<string>(serverType, "").then(draftData => {
      if (draftData) {
        content.value = draftData;
      }
    });
  }, [serverType]);

  const contentChanged = useComputed(() => {
    return content.value !== initialContent.value;
  });

  const isGenerating = useSignal(
    isGeneratingGuide &&
      guide.flowRunType ===
        (serverType === "transcript"
          ? GuideTaskType.GenerateGuide
          : GuideTaskType.GenerateReadingAndWhiteboard)
  );

  const reset = () => {
    content.value = initialContent.value;
  };

  useSignalEffect(() => {
    if (isGenerating.value) {
      localDraft.clear(serverType);
    }
  });
  useEffect(() => {
    if (!isGeneratingGuide) {
      content.value = initialContent.value;
    }
  }, [isGeneratingGuide, initialContent.value]);

  useEffect(() => {
    hasPanelUnSavedContent.value = {
      ...hasPanelUnSavedContent.value, 
      [serverType]: contentChanged.value
    };
    if (isGenerating.value) return;
    localDraft.save(serverType, content.value);
  }, [content.value, initialContent.value, contentChanged.value, serverType, localDraft, hasPanelUnSavedContent, isGenerating.value]);

  const save = useCallback(async () => {
    const { guideWidgetSetId, guideWidgetId } = guide;
    if (!guideWidgetSetId || !guideWidgetId) {
      return;
    }
    const res = await saveTrigger({
      guideWidgetSetId,
      guideWidgetId,
      [serverType]: content.value,
    });
    localDraft.clear(serverType);
    initialContent.value = content.value;
    return res;
  }, [content, saveTrigger, guide, serverType, localDraft, initialContent]);

  const value = {
    serverType,
    content,
    reset,
    save,
    isSaving,
    contentChanged,
    isGenerating,
  };

  return <MdEditorContext value={value}>{children}</MdEditorContext>;
};

export { MdEditorProvider, useMdEditorContext };
