"use client";
import {
  AlertDialog,
  AlertDialog<PERSON><PERSON>,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/app/components/common/alert-dialog";
import { But<PERSON> } from "@/app/components/common/button";
import fetcher, { post } from "@/app/utils/fetcher";
import { emitterPlus } from "@/lib/emitter";
import { GuideWidget, RawGuideSet } from "@/types/guide-widget";
import { useSignal } from "@preact-signals/safe-react";
import { CircleAlert, Sparkles } from "lucide-react";
import { FC, useCallback, useEffect, useState } from "react";
import useSWR from "swr";
import useSWRMutation from "swr/mutation";
import { GuideProvider, useGuideContext } from "../context/guide-context";
import AnchorPoint from "./anchor-point";
import { LineSplitEditorProvider } from "./line-split-editor/line-split-editor-context";
import { MdEditorContextType } from "./md-editor/md-editor-context";
import { PreviewVideoPanelBugfix } from "./panels-bugfix/preview-video-panel";
import { ReadingScriptPanelBugfix } from "./panels-bugfix/reading-script-panel";
import { VerbatimScriptPanelBugfix } from "./panels-bugfix/verbatim-script-panel";
import { WhiteboardScriptPanelBugfix } from "./panels-bugfix/whiteboard-script-panel";
import { InteractivePanel } from "./panels/interactive-panel";
import { LineSplitPanel } from "./panels/line-split-panel";
import { PreviewVideoWithEditor } from "./panels/preview-video-panel";
import { ReadingScriptPanel } from "./panels/reading-script-panel";
import { VerbatimScriptPanel } from "./panels/verbatim-script-panel";
import { WhiteboardScriptPanel } from "./panels/whiteboard-script-panel";

const GenerateGuide = (props: { mdContext?: MdEditorContextType | null }) => {
  const {
    guide,
    isGenerating,
    refresh,
    hasUnSavedContent,
    isFinish,
    isInit,
    hasPanelUnSavedContent,
    readingContext,
  } = useGuideContext();
  const { trigger, isMutating } = useSWRMutation(
    "/api/v1/guideWidget/produce/video",
    post
  );

  const showAlert = useSignal(false);

  const doGenerate = useCallback(async () => {
    const { guideWidgetId, guideWidgetSetId } = guide;
    trigger({
      guideWidgetId,
      guideWidgetSetId,
    });
    refresh?.();
  }, [guide, trigger, refresh]);

  const [isSaving, setIsSaving] = useState("init");
  useEffect(() => {
    if (isSaving === "ing") {
      if (!readingContext?.isMergeWholeAudio) {
        console.log("朗读稿合成配音完成");
        setIsSaving("init");
        doLastStep();
      }
    }
  }, [readingContext, isSaving]);

  const handleGenerate = useCallback(async () => {
    const { transcript, reading, boardscript } = hasPanelUnSavedContent.value;
    const isMergeReadingOpen = !readingContext?.isMergeAudioDisabled;
    const needShowAlert = [transcript, isMergeReadingOpen, boardscript].some(
      (item) => item
    );
    if (needShowAlert) {
      showAlert.value = true;
      return;
    }
    await doGenerate();
  }, [hasUnSavedContent, showAlert, doGenerate, readingContext]);

  const doLastStep = async () => {
    const { boardscript } = hasPanelUnSavedContent.value;
    if (boardscript) {
      if (props.mdContext) {
        await props.mdContext.save();
        console.log("正在保存：板书稿");
      }
    }
    await doGenerate();
  };
  const onDialogOk = useCallback(async () => {
    showAlert.value = false;
    const { transcript, reading, boardscript } = hasPanelUnSavedContent.value;
    const isMergeReadingOpen = !readingContext?.isMergeAudioDisabled;
    console.log(transcript, reading, boardscript);

    if (transcript) {
      console.log("正在保存：逐字稿");
      await emitterPlus.emit("saveTranscript");
      console.log("逐字稿保存完成");
    }

    if (reading) {
      console.log("正在保存：朗读稿", reading);
      // 如果有待 【合成整个配音】，则手动调用保存朗读稿
      if (isMergeReadingOpen) {
        console.log("正在保存：朗读稿", readingContext);
        await readingContext?.mergeEntireAudio();
        // 不知道为什么 isMergeWholeAudio会先 false 之后才是后 true等轮询结果
        setTimeout(() => {
          setIsSaving("ing");
          console.log("isSavingRef.current", isSaving);
        }, 1000);

        return false;
      } else {
        console.log("只是没有【生成配音】，不用管理");
      }
    }

    await doLastStep();
  }, [doGenerate, showAlert, props.mdContext, readingContext]);

  const onDialogCancel = useCallback(() => {
    showAlert.value = false;
  }, [showAlert]);

  const { transcript, reading, boardscript } = hasPanelUnSavedContent.value;
  const isMergeReadingOpen = !readingContext?.isMergeAudioDisabled;
  const showCircleAlert = [transcript, isMergeReadingOpen, boardscript].some(
    (item) => item
  );

  return (
    <div className="flex flex-row items-center gap-2">
      {showCircleAlert && <CircleAlert className="size-5 text-red-500" />}
      <Button
        type="outline"
        disabled={isInit || isGenerating || isFinish}
        loading={isMutating}
        onClick={handleGenerate}
        className="rounded-sm text-xs"
        icon={<Sparkles className="size-4" />}
      >
        生成视频
      </Button>
      <AlertDialog
        open={showAlert.value}
        onOpenChange={() => (showAlert.value = false)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>请注意</AlertDialogTitle>
            <AlertDialogDescription>
              <span className="text-zinc-600">
                是否自动提交所有修改，再生成视频？
              </span>
            </AlertDialogDescription>
            <AlertDialogFooter>
              <AlertDialogAction asChild>
                <>
                  <Button
                    type="outline"
                    onClick={onDialogCancel}
                    className="text-sm font-normal"
                  >
                    关闭
                  </Button>
                  <Button
                    type="primary"
                    onClick={onDialogOk}
                    className="text-sm font-normal"
                  >
                    确定
                  </Button>
                </>
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogHeader>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

interface GuideProps {
  mode?: string;
  guideSet: RawGuideSet;
  guideId: string;
  refreshSet?: () => void;
  children?: React.ReactNode;
}

export const Guide: FC<GuideProps> = ({
  mode,
  guideSet,
  guideId,
  refreshSet,
}) => {
  const [mdContext, setMdContext] = useState<MdEditorContextType | null>(null);
  const { data, mutate } = useSWR<GuideWidget>(
    () =>
      guideId
        ? `/api/v1/guideWidget/info?guideWidgetId=${guideId}&guideWidgetSetId=${guideSet.guideWidgetSetId}`
        : null,

    fetcher
  );

  if (!data) {
    return (
      <div className="flex h-full w-full items-center justify-center text-zinc-600">
        加载中...
      </div>
    );
  }

  return (
    <GuideProvider
      key={`guide-provider-${data.guideWidgetId}-${data.guideWidgetSetId}`}
      data={data}
      guideSet={guideSet}
      refresh={mutate}
      refreshSet={refreshSet}
      localDraftKey={mode === 'bugfix' ? 'guide-bugfix' : 'guide'}
    >
      <LineSplitEditorProvider>
        <main className="flex w-full min-w-[1150px] flex-1 flex-col items-center gap-4 p-2">
          <div id="video-section" className="flex w-full">
            {mode === "bugfix" ? (
              <PreviewVideoPanelBugfix />
            ) : (
              <PreviewVideoWithEditor />
            )}
          </div>

          <div id="transcript-section" className="flex w-full">
            {mode === "bugfix" ? (
              <VerbatimScriptPanelBugfix />
            ) : (
              <VerbatimScriptPanel />
            )}
          </div>

          <div id="reading-section" className="flex w-full">
            {mode === "bugfix" ? (
              <ReadingScriptPanelBugfix />
            ) : (
              <ReadingScriptPanel />
            )}
          </div>

          <div id="whiteboard-section" className="flex w-full">
            {mode === "bugfix" ? (
              <WhiteboardScriptPanelBugfix />
            ) : (
              <WhiteboardScriptPanel setMdContext={setMdContext} />
            )}
          </div>

          {mode !== "bugfix" && <GenerateGuide mdContext={mdContext} />}

          <div id="column-section" className="flex w-full">
            <LineSplitPanel />
          </div>

          <div id="interactive-section" className="flex w-full">
            <InteractivePanel />
          </div>

          <AnchorPoint />
        </main>
      </LineSplitEditorProvider>
    </GuideProvider>
  );
};
