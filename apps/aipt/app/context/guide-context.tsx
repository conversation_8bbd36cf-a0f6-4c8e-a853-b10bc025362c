"use client";

import { GuideStatus } from "@/types/base";
import { GuideWidget, RawGuideSet } from "@/types/guide-widget";
import {
  Signal,
  signal,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import {
  FC,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
} from "react";
import { ReadingEditorContextType } from "../components/reading-editor/reading-editor-context";
import { LocalDraftService } from "../service/local-draft-service";

type GuideContextType = {
  guideSet: RawGuideSet;
  guide: GuideWidget;
  localDraft: LocalDraftService;
  refreshSet?: () => void;
  refresh?: () => void;

  isInit: boolean;
  isProgress: boolean;
  isGenerating: boolean;
  isFinish: boolean;
  hasPanelUnSavedContent: Signal<{
    transcript: boolean;
    reading: boolean;
    boardscript: boolean;
  }>;
  hasUnSavedContent: Signal<boolean>;
  readingContext: ReadingEditorContextType | null;
  setReadingContext: (context: ReadingEditorContextType) => void;
};

const GuideContext = createContext<GuideContextType>({
  guideSet: {} as RawGuideSet,
  guide: {} as GuideWidget,
  localDraft: new LocalDraftService({
    guideWidgetSetId: "",
    guideWidgetId: "",
  }),
  refreshSet: () => {},
  refresh: () => {},
  isInit: false,
  isProgress: false,
  isGenerating: false,
  isFinish: false,
  hasPanelUnSavedContent: signal({
    transcript: false,
    reading: false,
    boardscript: false,
  }),
  hasUnSavedContent: signal(false),
  readingContext: null,
  setReadingContext: () => {},
});

const useGuideContext = () => useContext(GuideContext);

interface GuideProviderProps {
  children: React.ReactNode;
  data: GuideWidget;
  guideSet: RawGuideSet;
  refresh?: () => void;
  refreshSet?: () => void;
  readingContext?: ReadingEditorContextType | null;
  setReadingMdContext?: (context: ReadingEditorContextType) => void;
  localDraftKey?: string;
}

const GuideProvider: FC<GuideProviderProps> = ({
  children,
  data,
  guideSet,
  refresh,
  refreshSet,
  localDraftKey,
}) => {
  const { guideWidgetId, guideWidgetSetId } = data;

  const delayRefresh = useCallback(() => {
    setTimeout(() => {
      refresh?.();
    }, 200);
  }, [refresh]);

  const readingContext = useSignal<ReadingEditorContextType | any>(null);
  const setReadingContext = useCallback(
    (context: ReadingEditorContextType | any) => {
      if (context) {
        const relevantProps = ['batchGenerateLoading', 'isBatchGenerateDisabled', 'isContentChanged', 
                             'isMergeAudioDisabled', 'isMergeWholeAudio', 'isSaving'];
        const hasChanged = relevantProps.some(prop => 
          readingContext.value?.[prop] !== context[prop]
        );
        if (hasChanged) {
          // console.log("特定属性发生变化啦", context);
          readingContext.value = context;
        }
      }
    },
    [readingContext]
  );

  const localDraftService = useMemo(() => {
    return new LocalDraftService({
      guideWidgetSetId: guideWidgetSetId.toString(),
      guideWidgetId: guideWidgetId.toString(),
      key: localDraftKey,
    });
  }, [guideWidgetId, guideWidgetSetId, localDraftKey]);

  const isInit = useMemo(() => {
    return data.guideWidgetStatus === GuideStatus.Init;
  }, [data]);

  const isProgress = useMemo(() => {
    return (
      data.guideWidgetStatus === GuideStatus.Progress ||
      data.guideWidgetStatus === GuideStatus.Init
    );
  }, [data]);

  const isGenerating = useMemo(() => {
    return data.guideWidgetStatus === GuideStatus.Loading;
  }, [data]);

  const isFinish = useMemo(() => {
    return data.guideWidgetStatus === GuideStatus.Finish;
  }, [data]);

  // 每个面板是否保存了内容
  const hasPanelUnSavedContent = useSignal({
    transcript: false,
    reading: false,
    boardscript: false,
  });

  // 是否存在未保存的内容
  const hasUnSavedContent = useSignal(false);
  useSignalEffect(() => {
    hasUnSavedContent.value = Object.values(hasPanelUnSavedContent.value).some(
      (item) => item === true
    );
  });

  useEffect(() => {
    const { guideWidgetStatus } = data;
    const timer = setInterval(() => {
      if (guideWidgetStatus === GuideStatus.Loading) {
        refresh?.();
      } else {
        clearInterval(timer);
      }
    }, 10 * 1000);
    return () => clearInterval(timer);
  }, [refresh, data]);

  const value = {
    guideSet,
    guide: data,
    localDraft: localDraftService,
    refreshSet,
    refresh: delayRefresh,
    isInit,
    isGenerating,
    isProgress,
    isFinish,
    hasPanelUnSavedContent,
    hasUnSavedContent,
    setReadingContext,
    readingContext: readingContext.value,
  };

  return <GuideContext value={value}>{children}</GuideContext>;
};

export { GuideProvider, useGuideContext };
