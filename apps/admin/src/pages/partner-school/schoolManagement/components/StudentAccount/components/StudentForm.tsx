import { addStudent, editStudent } from '@/services/student-management';
import { StudentInfo } from '@/services/student-management/type';
import { Form, Input, message, Modal, Radio } from 'antd';
import React, { useEffect } from 'react';
interface StudentFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  initialValues?: Partial<StudentInfo>;
  schoolId: number;
  gradeId: number;
  classId: number;
  isEdit?: boolean;
}

const StudentForm: React.FC<StudentFormProps> = ({
  visible,
  onCancel,
  onSuccess,
  initialValues,
  schoolId,
  gradeId,
  classId,
  isEdit = false,
}) => {
  const [form] = Form.useForm();

  // 当表单显示或初始值变化时重置表单
  useEffect(() => {
    console.log('visible', visible, initialValues);

    if (visible) {
      form.resetFields();
      if (initialValues) {
        form.setFieldsValue(initialValues);
      }
    }
  }, [visible, initialValues, form]);

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (isEdit && initialValues?.userID) {
        // 编辑学生
        const res = await editStudent({
          userID: initialValues.userID,
          schoolID: schoolId,
          ...values,
        });
        if (res.status === 200) {
          message.success('学生信息更新成功');
          onSuccess();
        } else {
          message.error(res.message);
        }
      } else {
        // 添加学生
        const res = await addStudent({
          schoolId,
          gradeId,
          classId,
          ...values,
        });
        if (res.status === 200) {
          message.success('学生添加成功');
          onSuccess();
        } else {
          message.error(res.message);
        }
      }
    } catch (error) {
      console.error('表单提交失败:', error);
    }
  };

  return (
    <Modal
      className="student-form-modal"
      title={isEdit ? '编辑学生' : '添加学生'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      maskClosable={false}
      destroyOnHidden
    >
      <Form form={form} layout="vertical" initialValues={{ userIsTest: 1 }}>
        <Form.Item
          name="userName"
          label="学生姓名"
          rules={[{ required: true, message: '请输入学生姓名' }]}
        >
          <Input placeholder="请输入学生姓名" />
        </Form.Item>

        <Form.Item
          rules={[{ required: true, message: '请输入学号' }]}
          name="userNumber"
          label="学号"
        >
          <Input placeholder="请输入学号" />
        </Form.Item>

        <Form.Item
          name="userIsTest"
          label="是否测试账号"
          rules={[{ required: true, message: '请选择是否为测试账号' }]}
        >
          <Radio.Group>
            <Radio value={1}>否</Radio>
            <Radio value={2}>是</Radio>
          </Radio.Group>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default StudentForm;
