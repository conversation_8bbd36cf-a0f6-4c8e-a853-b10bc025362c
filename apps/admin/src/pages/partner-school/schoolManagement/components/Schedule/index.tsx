import { getGradeScheduleTemplateDetail, getSchedule } from '@/services/schedule-management';
import { ScheduleData } from '@/services/schedule-management/type';
import { Semester } from '@/services/semester-management/type';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import GradeTree, { GradeTreeProps, NodeInfo, NodeType } from '../../../../../components/GradeTree';
import { ScheduleContext } from '../../contexts/ScheduleContext';
import ScheduleContent from './components/ScheduleContent';
import useSemesterWeekSelector, {
  WeekInfo,
} from './components/SemesterWeekSelector/useSemesterWeekSelector';

// 节点信息类型
interface SelectedNodeInfo {
  nodeType: NodeType;
  nodeId: number;
  scheduleName: string;
  nodeInfo?: NodeInfo<NodeType>;
}

const ScheduleManagement: React.FC = () => {
  const { schoolInfo } = useModel('schoolModel');
  // 状态管理
  const [selectedNodeInfo, setSelectedNodeInfo] = useState<SelectedNodeInfo | null>(null);
  const [loading, setLoading] = useState<{ tree: boolean; schedule: boolean }>({
    tree: false,
    schedule: false,
  });
  const [scheduleData, setScheduleData] = useState<ScheduleData | null>(null);
  const [editMode, setEditMode] = useState<boolean | 'class' | 'grade'>(false);
  const [semesterInfo, setSemesterInfo] = useState<Semester | null>(null);
  const [weekInfo, setWeekInfo] = useState<WeekInfo | null>(null);
  const [currentGradeId, setCurrentGradeId] = useState<number | null>(null);
  const { SemesterWeekSelector, hasSemesterData, setStartDate } = useSemesterWeekSelector({
    editMode: !!editMode,
    grade: currentGradeId || 0,
    onChange: (weekInfo, semesterInfo) => {
      setWeekInfo(weekInfo);
      setSemesterInfo(semesterInfo);
    },
  });
  // 获取课表数据
  const fetchScheduleData = useCallback(
    async (nodeType: NodeType, nodeId: number) => {
      if (!weekInfo || !semesterInfo || !semesterInfo?.semester_id) {
        // message.error('请先选择学期和周');
        return;
      }
      if (weekInfo.weekNumber === 0) {
        setScheduleData(null);
        return;
      }
      console.log('fetchScheduleData', { nodeType, nodeId, semesterInfo });
      setLoading((prev) => ({ ...prev, schedule: true }));

      try {
        if (nodeType === NodeType.Grade) {
          const response = await getGradeScheduleTemplateDetail({
            grade: Number(nodeId),
            school_id: Number(schoolInfo?.schoolId),
            school_year_id: Number(schoolInfo?.schoolYear?.school_year_id) || 1,
            semester_id: semesterInfo?.semester_id,
            week_start_date: weekInfo?.startDate,
            week_end_date: weekInfo?.endDate,
            // semester_id: Number(schoolInfo?.semesterId) || 1
          });
          setScheduleData(response.data);
        } else if (nodeType === NodeType.Class) {
          const response = await getSchedule({
            school_id: Number(schoolInfo?.schoolId),
            school_year_id: Number(schoolInfo?.schoolYear?.school_year_id) || 1,
            semester_id: semesterInfo?.semester_id,
            start_date: weekInfo?.startDate,
            end_date: weekInfo?.endDate,
            type: 'class',
            class_id: Number(nodeId),
            week: 1,
          });
          setScheduleData(response.data);
        } else {
          setScheduleData(null);
        }
      } catch (error) {
        console.error('获取课表数据失败:', error);
        message.error('获取课表数据失败');
        setScheduleData(null);
      } finally {
        setLoading((prev) => ({ ...prev, schedule: false }));
      }
    },
    [schoolInfo?.schoolId, weekInfo, semesterInfo],
  );

  useEffect(() => {
    const selectedNodeType = selectedNodeInfo?.nodeType;
    console.log('selectedNodeType', selectedNodeType);
    if (selectedNodeType === NodeType.Grade || selectedNodeType === NodeType.Class) {
      fetchScheduleData(selectedNodeType, selectedNodeInfo?.nodeId || 0);
    }
  }, [selectedNodeInfo, semesterInfo, weekInfo]);

  // 处理树节点选择
  const handleTreeSelect = useCallback<GradeTreeProps['onNodeSelect']>(
    (selectedKey: React.Key, selectedNodeType, nodeInfo) => {
      console.log('handleTreeSelect', selectedKey, selectedNodeType, nodeInfo);

      if (!selectedKey) return;

      // 只有年级节点和班级节点可以选中
      if (selectedNodeType === NodeType.Grade || selectedNodeType === NodeType.Class) {
        // 查找节点名称
        let scheduleName = '';
        let nodeId = 0;

        if (selectedNodeType === NodeType.Grade && nodeInfo) {
          // 断言为年级节点类型
          const gradeInfo = nodeInfo as NodeInfo<NodeType.Grade>;
          scheduleName = gradeInfo.grade_name || '';
          nodeId = gradeInfo.grade_id;
          setCurrentGradeId(gradeInfo.grade_id);
        } else if (selectedNodeType === NodeType.Class && nodeInfo) {
          // 断言为班级节点类型
          const classInfo = nodeInfo as NodeInfo<NodeType.Class>;
          scheduleName = `${classInfo.gradeInfo.grade_name} ${classInfo.class_name}`;
          nodeId = classInfo.class_id;
          setCurrentGradeId(classInfo.gradeInfo.grade_id);
        }

        // 设置选中节点信息
        setSelectedNodeInfo({
          nodeType: selectedNodeType,
          nodeId,
          scheduleName,
          nodeInfo,
        });

        // 退出编辑模式
        setEditMode(false);
      }
    },
    [],
  );

  // 保存/取消处理
  const handleScheduleAction = useCallback(
    (action: 'save' | 'cancel') => {
      setEditMode(false);

      // 刷新课表数据
      if (selectedNodeInfo) {
        fetchScheduleData(selectedNodeInfo.nodeType, selectedNodeInfo.nodeId);
      }
    },
    [selectedNodeInfo, fetchScheduleData],
  );

  // 导入成功处理
  const handleImportSuccess = useCallback(() => {
    message.success('课表导入成功');

    // 刷新数据
    if (selectedNodeInfo) {
      fetchScheduleData(selectedNodeInfo.nodeType, selectedNodeInfo.nodeId);
    }
  }, [selectedNodeInfo, fetchScheduleData]);

  const GradeTreeMemo = useMemo(() => {
    return <GradeTree disableSelection={!!editMode} onNodeSelect={handleTreeSelect} />;
  }, [editMode, handleTreeSelect]);

  return (
    <ScheduleContext
      value={{
        name: '123',
        selectedNodeInfo,
        scheduleData,
        hasSemesterData,
        loading,
        editMode,
        schoolInfo,
        semesterInfo,
        handleImportSuccess,
        setEditMode,
        SemesterWeekSelector,
        handleScheduleAction,
        setStartDate,
        weekInfo,
      }}
    >
      <div className="flex bg-transparent rounded overflow-hidden h-[calc(100vh-308px)]">
        <div
          className={`transition-all duration-300 h-full overflow-hidden ${editMode === 'class' ? 'w-0' : 'w-60 mr-3'}`}
        >
          {GradeTreeMemo}
        </div>

        <div className="h-full flex-1 p-4 overflow-y-auto bg-white">
          {/* {renderScheduleContent()} */}
          <ScheduleContent />
        </div>
      </div>
    </ScheduleContext>
  );
};

export default ScheduleManagement;
