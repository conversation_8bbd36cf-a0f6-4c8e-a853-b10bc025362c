import { FEEDBACK_TYPE } from "@/enums";
import { FeedbackSource, useFeedbackByType } from "@/hooks/useReportFeedback";
import { AiCourseDetail } from "@/types/assign/preview";
import { CourseWidgetDetail } from "@repo/core/components/ai-course-preview/type";
import { useCallback, useRef } from "react";

const FEEDBACK_TYPE_MAP = {
  guide: FEEDBACK_TYPE.COURSE,
  interactive: FEEDBACK_TYPE.COURSE,
  exercise: FEEDBACK_TYPE.QUESTION,
  video: FEEDBACK_TYPE.VIDEO,
};

export function useAiCourseReportModel({
  aiCourseData,
  widgetData,
}: {
  aiCourseData?: AiCourseDetail;
  widgetData?: CourseWidgetDetail;
}) {
  const { routeToFeedback } = useFeedbackByType();
  // 上报需要的，暂时不会上移
  const questionIndex = useRef<number>(0);
  const handleQuestionChange = useCallback(({ index }: { index: number }) => {
    questionIndex.current = index;
  }, []);

  // TODO: 把model抽成context，然后这里直接调用context里的方法
  const reportWidget = useCallback(() => {
    const activeWidgetInfo = widgetData;
    if (!aiCourseData || !activeWidgetInfo) return;
    const type =
      FEEDBACK_TYPE_MAP[activeWidgetInfo.type] || FEEDBACK_TYPE.COURSE;

    const commonParams = {
      feedbackSource: FeedbackSource.COURSE_PREVIEW,
      subjectId: aiCourseData.subject ?? -1,
      feedbackPhaseId: aiCourseData.phase ?? -1,
      courseId: aiCourseData.lessonId,
      version: aiCourseData.publishVersion ?? "",
      widgetIndex: activeWidgetInfo.index ?? -1,
      feedbackWidgetName: activeWidgetInfo.name ?? "",
    };

    if (type === FEEDBACK_TYPE.QUESTION) {
      const question =
        activeWidgetInfo.data[questionIndex.current]?.questionInfo;

      return routeToFeedback(FEEDBACK_TYPE.QUESTION, {
        ...commonParams,
        questionId: question.questionId,
        feedbackQuestionVersionId: question.questionVersionId,
      });
    }
    if (type === FEEDBACK_TYPE.VIDEO) {
      return routeToFeedback(FEEDBACK_TYPE.VIDEO, {
        ...commonParams,
        // url: activeWidgetInfo.data.url,
      });
    }
    return routeToFeedback(FEEDBACK_TYPE.COURSE, {
      ...commonParams,
    });
  }, [aiCourseData, routeToFeedback, widgetData]);

  return {
    handleQuestionChange,
    reportWidget,
  };
}
