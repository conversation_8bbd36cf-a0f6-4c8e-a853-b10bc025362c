import { useApp } from "@/hooks";
import { AiCourseDetail } from "@/types/assign/preview";
import { ScrollArea } from "@/ui/scroll-area";
import {
  AiCoursePreviewHandler,
  CourseView,
} from "@repo/core/components/ai-course-preview";
import { CourseWidgetDetail } from "@repo/core/components/ai-course-preview/type";
import { Card } from "./Card";
import WidgetReportButton from "./WidgetReportButton";
import "./widgetLoaderPolyfill.css";

export interface AiCoursePreviewProps {
  data?: AiCourseDetail;
  isFetchingData: boolean;
  activeIndex: number;
  widgetData?: CourseWidgetDetail;
  isWidgetDataLoading: boolean;
  handleActiveIndexChange: (index: number) => void;
  onQuestionChange: React.ComponentProps<typeof CourseView>["onQuestionChange"];
  reportWidget: () => void;
  handlerRef?: React.RefObject<AiCoursePreviewHandler | null>;
  onEnterFullscreen?: () => void;
  onExitFullscreen?: () => void;
}

export default function AiCoursePreview({
  data,
  isFetchingData,
  activeIndex,
  widgetData,
  isWidgetDataLoading,
  handleActiveIndexChange,
  onQuestionChange,
  reportWidget,
  handlerRef,
  onEnterFullscreen,
  onExitFullscreen,
}: AiCoursePreviewProps) {
  const { statusBarHeight } = useApp();

  return (
    <ScrollArea className="h-full w-full" orientation="vertical">
      <div
        className="w-full flex-1 items-start gap-2.5 overflow-hidden pb-4 pl-6 pr-4"
        // TODO：这里的高度需要改造成适配父容器高度的组件
        style={{
          height: `calc(100vh - 4.375rem - ${statusBarHeight}px)`,
          width: `100%`,
        }}
      >
        <Card className="h-full w-full flex-1 overflow-hidden px-5 py-4">
          <div className="flex h-full flex-1 flex-col overflow-hidden">
            <div className="text-gray-1 text-base font-medium leading-6 tracking-wider">
              课堂讲解
            </div>
            <div className="border-1 border-primary-5 mb-3.5 mt-4 w-full"></div>

            <div className="relative flex flex-1 overflow-hidden">
              <CourseView
                className="flex-1"
                progressData={data?.lessonShowInfo.widgetList.map((e) => {
                  return {
                    index: e.widgetIndex,
                    type: e.widgetType,
                    name: e.widgetName,
                    data: e.data,
                    // NOTE：这个hidden后端没返回，先写死
                    hidden: 0,
                  };
                })}
                isFetchingProgressData={isFetchingData}
                activeIndex={activeIndex}
                widgetData={widgetData}
                isWidgetDataLoading={isWidgetDataLoading}
                onActiveIndexChange={handleActiveIndexChange}
                onQuestionChange={onQuestionChange}
                // 问题反馈
                WidgetLoaderSuffixElement={
                  <WidgetReportButton onReport={reportWidget} />
                }
                // 禁用点击播放
                guideWidgetProps={{
                  clickToPlay: false,
                  loop: false,
                  moveToBeginningWhenEnded: false,
                }}
                handlerRef={handlerRef}
                onEnterFullscreen={onEnterFullscreen}
                onExitFullscreen={onExitFullscreen}
                widgetLoaderClassName="tch-widget-loader-polyfill"
              />
            </div>
          </div>
        </Card>
      </div>
    </ScrollArea>
  );
}
