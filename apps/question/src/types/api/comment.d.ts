declare namespace API {
  namespace Comment {
    /**
     * 审核任务列表项
     */
    interface CommentItem {
      commentId: number; // 评论ID
      commentContent: string; // 评论内容
      parentContent: string; // 上级评论内容 (若当前为二级评论)
      commentStatus: number; // 评论状态 1=仅自己可见, 2=问题反馈, 3=正常, -1=删除
      llmScore: number; // LLM评分 -1=问题反馈, 0=违规, 1=无实质信息的评论, 2=轻度学习相关, 3=高质量学习评论
      likesCount: number; // 点赞数
      repliesCount: number; // 二级评论数
      isTeacherRecommended: boolean; // 老师推荐
      userId: number; // 评论者ID
      userName: string; // 评论者姓名
      schoolName: string; // 评论者学校
      className: string; // 评论者归属 ?? 评论者班级
      commentScope: number; // 发布范围 1=本班, 2=年级, 3=本校, 4=公开
      courseName: string; // 所属课程 ??
      widgetName: string; // 所属模块 ??
      publishedAt: string; // 发布时间（字符串）??
      // isLiked: boolean; // 是否已点赞??
    }

    interface CommentListReference {
      comments: CommentItem[];
      total: number;
    }

    interface CommentListRequestParams {
      userId?: number; // 评论者ID
      content?: string; // 评论者内容
      status?: number; // 评论状态
      llmScore?: number; // LLM评分
      isTeacherRecommended?: number; // 老师推荐
      page: number; // 页码
      pageSize: number; // 每页条数
    }

    interface CommentModifyCommentScopeRequestParams {
      commentId: number;
      commentScope: number;
    }

    interface CommentModifyStatusRequestParams {
      commentId: number;
      /**
       * 评论状态，0：已删除、1：仅自己可见、2：问题反馈、3：正常
       */
      status: number;
      /**
       * LLM打分，范围0到3
       */
      llmScore?: number;
    }

    interface CommentEnumConstantData {
      /**
       * LLM打分，范围0到3
       */
      llmScore: EnumConstantItem<0 | 1 | 2 | 3>[];
      /**
       * 评论状态
       */
      status: EnumConstantItem<0 | 1 | 2 | 3>[];
      /**
       * 老师推荐
       */
      teacherRecommended: EnumConstantItem<0 | 1>[];
      /**
       * 评论范围
       */
      commentScope: EnumConstantItem<1 | 2 | 3 | 4>[];
      /**
       * 举报类型
       */
      reportType: EnumConstantItem<1 | 2 | 3 | 4 | 5 | 6 | 7>[];
      /**
       * 举报处理状态
       */
      reportProcessedStatus: EnumConstantItem<1 | 2 | 3>[];
    }

    interface HighQualityCommentListRequestParams extends CommentListRequestParams {
      auditStatus?: number; // 审核状态
      schoolName?: string; // 学校名称
    }

    export interface ReportCommentItem {
      reportId: number; // 举报ID
      commentId: number; // 评论ID
      reportType: number; // 举报类型 “信息有误”、“不友善”、“网络暴力”、“学习无关”、“色情低俗”、“政治敏感”、“其他”
      reportExtraReason: string; // 举报原因
      commentContent: string; // 评论内容
      parentContent: string; // 上级评论内容 (若当前为二级评论)
      reportCount: number; // 举报次数
      commentStatus: number; // 评论状态 1=仅自己可见, 2=问题反馈, 3=正常, -1=删除
      commentScope: number; // 发布范围 1=本班, 2=年级, 3=本校, 4=公开
      reportUserId: number; // 举报者ID
      reportUserName: string; // 举报者姓名
      reportUserSchool: string; // 举报者学校
      reportUserClass: string; // 举报者班级
      commentUserId: number; // 评论者ID
      commentUserName: string; // 评论者姓名
      commentSchoolName: string; // 评论者学校
      commentClassName: string; // 评论者归属 ?? 评论者班级
      reportCreateAt: string; // 举报时间
      reportProcessedStatus: number; // 举报处理状态 1=待处理, 2=已处理, 3=已关闭
    }

    interface ReportCommentListRequestParams extends CommentListRequestParams {
      reportType?: number; // 举报类型
      reportUserId?: number; // 举报者ID
      reportProcessedStatus?: number; // 举报处理状态
    }

    interface CommentModifyReportStatusRequestParams {
      reportId: number;
      operationType: number; // 1=仅自己可见, 2=关闭, 3=删除
    }
  }

  // 举报评论列表项
}
