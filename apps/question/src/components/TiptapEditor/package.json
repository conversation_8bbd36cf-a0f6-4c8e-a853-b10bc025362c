{"name": "@repo/tiptap-editor", "version": "1.0.0", "description": "", "keywords": [], "license": "ISC", "author": "", "main": "src/index.tsx", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@remixicon/react": "^4.6.0", "@repo/core": "workspace:*", "@repo/lib": "workspace:*", "@repo/ui": "workspace:*", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-highlight": "^2.12.0", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/html": "^2.12.0", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@uiw/react-color": "^2.5.5", "antd": "^5.24.1", "axios": "^1.9.0", "classnames": "^2.5.1", "hammerjs": "^2.0.8", "mathlive": "^0.104.0", "md5": "^2.3.0", "mitt": "^3.0.1", "remixicon": "^4.6.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/react": "^19.1.3"}, "packageManager": "pnpm@10.10.0"}