import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import EnumManager from './enumManager';

dayjs.extend(utc);
dayjs.extend(timezone);

export const scrollToTop = (cls: string) => {
  const pageContainer = document.querySelector(cls);
  if (pageContainer) {
    pageContainer.scrollTo({ top: 0, behavior: 'smooth' });
  }
};

export const addQuestionNoForList = (list: API.QuestionItemType[]) => {
  return list.map((item, idx) => ({ ...item, questionNo: idx + 1 }));
};

export const parseQueryString = (query: string) => {
  const params = new URLSearchParams(query);
  const result: Record<string, string> = {};
  params.forEach((value, key) => {
    result[key] = value;
  });
  return result;
};

// 从 url 中获取 token 并保存到 localStorage
export const saveTokenFormUrl = () => {
  const { search } = window.location;
  const query = parseQueryString(search);
  const { token } = query;
  if (token) {
    localStorage.setItem('token', token);
  }
};
// 移除 url 中的指定参数
export const removeFieldFromUrl = (url: string, field: string) => {
  const urlObj = new URL(url);
  urlObj.searchParams.delete(field);
  return urlObj.toString();
};

// 获取登录 url
export const getLoginUrl = (isRoot = false, error = '') => {
  if (isRoot) {
    return `${REMOTE_LOGIN_URL}?redirect=${window.location.origin}&error=${error}`;
  }
  const url = removeFieldFromUrl(window.location.href, 'token');
  return `${REMOTE_LOGIN_URL}?redirect=${url}&error=${error}`;
};

// 将页面状态保存到 localStorage
export const savePageState = (key: string, state: any) => {
  const token = localStorage.getItem('token');
  const listParamsInfo = {
    token,
    state,
  };
  localStorage.setItem(key, JSON.stringify(listParamsInfo));
};

// 从 localStorage 中获取页面状态
export const getPageState = (key: string) => {
  const pageState = localStorage.getItem(key);
  if (!pageState) {
    return null;
  }
  const { state, token } = JSON.parse(pageState);
  const currentToken = localStorage.getItem('token');
  if (token !== currentToken) {
    return null;
  }
  return state;
};

/**
 * 为富文本中的 img 标签添加 host
 * @param richText 原始富文本字符串
 * @param host 要添加的主机地址 (如 'https://example.com/')
 * @returns 处理后的富文本字符串
 */
export function addHostToImgSrc(richText: string, host: string): string {
  // 确保host以斜杠结尾
  const normalizedHost = host.endsWith('/') ? host : host + '/';

  // 使用正则表达式匹配并替换img标签的src属性
  return richText.replace(/<img[^>]+src="([^"]*)"[^>]*>/g, (match, src) => {
    // 如果src已经是完整URL或者是data URL，则不处理
    if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {
      return match;
    }

    // 移除src可能的前导斜杠
    const cleanSrc = src.startsWith('/') ? src.substring(1) : src;

    // 构建新的img标签
    return match.replace(
      `src="${src}"`,
      `src="${normalizedHost}${cleanSrc}" data-origin-src="${src}"`,
    );
  });
}

export const getBeiJingTimeStr = (
  zeroZoneTimestamp: number,
  format: string = 'YYYY/MM/DD HH:mm:ss',
) => {
  return dayjs.utc(zeroZoneTimestamp).format(format);
};
export const getFormatTimeStr = (timpstamp: number, format: string = 'YYYY/MM/DD HH:mm:ss') => {
  return dayjs(timpstamp).format(format);
};

/**
 * 将业务树列表转换为树结构
 * @param list 业务树列表
 * @returns 树结构
 */
export const transformBizTreeListToTreeStructure = (
  list: API.Common.TreeListItem[],
  materialList:
    | EnumManager<
        (API.EnumConstantItem<number> & {
          label: string;
          text: string;
        })[]
      >
    | undefined,
): API.Common.TreeListItem[] => {
  const hasNoMaterial = list.some((item) => !item.material);
  if (hasNoMaterial) {
    return list;
  }

  const materialOrder: number[] = [];
  const materialMap = new Map();
  list.forEach((item) => {
    if (!materialMap.has(item.material)) {
      materialMap.set(item.material, {
        label: materialList?.getLabelByValue(item.material) || '',
        value: item.material,
        isMaterial: true,
        children: [],
      });
      materialOrder.push(item.material);
    }
    materialMap.get(item.material).children.push({
      label: item.label,
      value: item.value,
      isMaterial: false,
    });
  });

  return materialOrder.map((material) => materialMap.get(material));
};

/**
 * 将文件转换为base64
 * @param list 业务树列表
 * @returns 树结构
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
};

// 判断两个整数数组是否相等
export const compareNumberArrayEqual = (arr1: number[], arr2: number[]) => {
  if (!Array.isArray(arr1) || !Array.isArray(arr2) || arr1.length !== arr2.length) {
    return false;
  }

  arr1.sort((a, b) => a - b);
  arr2.sort((a, b) => a - b);
  return arr1.join(',') === arr2.join(',');
};

// 删除对象中的 null 值字段
export const removeNullFields = (obj: Record<string, any>) => {
  return Object.entries(obj).reduce((acc: Record<string, any>, [key, value]) => {
    if (value !== null && value !== undefined && !Number.isNaN(value)) {
      acc[key] = value;
    }
    return acc;
  }, {});
};
