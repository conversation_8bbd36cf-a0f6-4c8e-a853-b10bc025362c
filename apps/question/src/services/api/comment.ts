import { request } from '@umijs/max';

// 获取评论列表
export const fetchCommentList = (data: API.Comment.CommentListRequestParams) => {
  return request<API.Response<API.CommentPageData<API.Comment.CommentItem>>>(
    `${COMMENT_API_HOST}/api/v1/admin/comments/list`,
    {
      method: 'POST',
      data,
    },
  );
};

// 获取评论枚举常量
export const queryCommentEnumConstants = () => {
  return request<API.Response<API.Comment.CommentEnumConstantData>>(
    `${COMMENT_API_HOST}/api/v1/admin/comment/constants`,
    {
      method: 'GET',
    },
  );
};

// 修改评论状态
export const modifyCommentStatus = (data: API.Comment.CommentModifyStatusRequestParams) => {
  return request<API.Response<API.PageData<API.Comment.CommentItem>>>(
    `${COMMENT_API_HOST}/api/v1/admin/comment/status`,
    {
      method: 'POST',
      data,
    },
  );
};

// 高质量评论列表
export const fetchHighQualityCommentList = (
  data: API.Comment.HighQualityCommentListRequestParams,
) => {
  return request<API.Response<API.CommentPageData<API.Comment.CommentItem>>>(
    `${COMMENT_API_HOST}/api/v1/admin/comment/quality`,
    {
      method: 'POST',
      data,
    },
  );
};

// 修改评论可见范围
export const modifyCommentScope = (data: API.Comment.CommentModifyCommentScopeRequestParams) => {
  return request<API.Response<string>>(`${COMMENT_API_HOST}/api/v1/admin/comment/scope/update`, {
    method: 'POST',
    data,
  });
};

// 举报评论列表
export const fetchReportCommentList = (data: API.Comment.ReportCommentListRequestParams) => {
  return request<API.Response<API.CommentPageData<API.Comment.ReportCommentItem>>>(
    `${COMMENT_API_HOST}/api/v1/admin/reports/list`,
    {
      method: 'POST',
      data,
    },
  );
};

// 修改举报状态
export const modifyReportStatus = (data: API.Comment.CommentModifyReportStatusRequestParams) => {
  return request<API.Response<string>>(`${COMMENT_API_HOST}/api/v1/admin/reports/handle`, {
    method: 'POST',
    data,
  });
};
