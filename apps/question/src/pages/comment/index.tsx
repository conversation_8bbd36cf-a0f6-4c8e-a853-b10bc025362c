import { queryCommentEnumConstants } from '@/services/api';
import { PageContainer } from '@ant-design/pro-components';
import { Outlet } from '@umijs/max';
import { Spin } from 'antd';
import React, { useEffect, useState } from 'react';

const Comment: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [sharedData, setSharedData] = useState<API.Comment.CommentEnumConstantData | null>(null);
  useEffect(() => {
    setLoading(true);
    queryCommentEnumConstants()
      .then((res) => {
        if (res.code === 0) {
          setSharedData(res.data);
          return;
        }
        setSharedData(null);
      })
      .catch((err) => {
        console.error('获取枚举常量失败:', err);
        setSharedData(null);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);
  return (
    <PageContainer>
      <Spin spinning={loading}>
        {/* 通过context或props将数据传递给子页面 */}
        <Outlet context={{ commentEnumConstants: sharedData }} />
      </Spin>
    </PageContainer>
  );
};

export default Comment;
