// @ts-nocheck
import * as React from 'react';
import { v4 as uuidv4 } from "uuid";
import { Canvas, CanvasRef } from '../Canvas';
import { CanvasPath, ExportImageParams, Point } from '../types';

export type ReactSketchCanvasStates = {};

/* Props validation */
export interface ReactSketchCanvasProps {
  id?: string;
  width?: string;
  height?: string;
  className?: string;
  strokeColor?: string;
  canvasColor?: string;
  backgroundImage?: string;
  exportWithBackgroundImage?: boolean;
  preserveBackgroundImageAspectRatio?: string;
  strokeWidth?: number;
  eraserWidth?: number;
  allowOnlyPointerType?: string;
  onChange?: (updatedPaths: CanvasPath[]) => void;
  onStroke?: (path: CanvasPath, isEraser: boolean) => void;
  style?: React.CSSProperties;
  svgStyle?: React.CSSProperties;
  withTimestamp?: boolean;
  // 监听undo/redo状态，配合外层组件优化按钮显示状态
  onUndoRedoChange?: ({ undo, redo }: { undo: boolean, redo: boolean }) => void;

}

export interface ReactSketchCanvasRef {
  eraseMode: (erase: boolean) => void;
  clearCanvas: () => void;
  undo: () => void;
  redo: () => void;
  exportImage: (ExportImageParams) => Promise<string>;
  exportSvg: () => Promise<string>;
  exportPaths: () => Promise<CanvasPath[]>;
  loadPaths: (paths: CanvasPath[]) => void;
  getSketchingTime: () => Promise<number>;
  resetCanvas: () => void;
}

export const ReactSketchCanvas = React.forwardRef<
  ReactSketchCanvasRef,
  ReactSketchCanvasProps
>((props, ref) => {
  const {
    id = 'react-sketch-canvas',
    width = '100%',
    height = '100%',
    className = '',
    canvasColor = 'white',
    strokeColor = 'red',
    backgroundImage = '',
    exportWithBackgroundImage = false,
    preserveBackgroundImageAspectRatio = 'none',
    strokeWidth = 4,
    eraserWidth = 8,
    allowOnlyPointerType = 'all',
    style = {
      border: '0.0625rem solid #9c9c9c',
      borderRadius: '0.25rem',
    },
    svgStyle = {},
    onChange = (_paths: CanvasPath[]): void => {},
    onStroke = (_path: CanvasPath, _isEraser: boolean): void => {},
    withTimestamp = false,
    onUndoRedoChange = ({ undo, redo }: { undo: boolean, redo: boolean }): void => {},
  } = props;

  const svgCanvas = React.createRef<CanvasRef>();
  const [drawMode, setDrawMode] = React.useState<boolean>(true);
  const [isDrawing, setIsDrawing] = React.useState<boolean>(false);
  const [resetStack, setResetStack] = React.useState<CanvasPath[]>([]);
  const [undoStack, setUndoStack] = React.useState<CanvasPath[]>([]);
  const [currentPaths, setCurrentPaths] = React.useState<CanvasPath[]>([]);
  const [newPaths, setNewPaths] = React.useState<CanvasPath | null>(null);
  const dubblePointTimer = React.createRef<NodeJS.Timeout | null>(null);

  // const liftStrokeUp = React.useCallback((): void => {
  //   if (!newPaths) return;
  //   const lastStroke = newPaths;
  //   // const lastStroke = newPaths;
  //   // console.log('liftStrokeUp', lastStroke === newPaths);

  //   // if (lastStroke === null) {
  //   //   console.warn('No stroke found!');
  //   //   return;
  //   // }

  //   onStroke(lastStroke, !lastStroke.drawMode);
  // }, [isDrawing, newPaths]);

  // React.useEffect(() => {
  //   liftStrokeUp();
  // }, [isDrawing]);

  React.useEffect(() => {
    onChange(currentPaths);
  }, [currentPaths]);
  
  React.useEffect(() => {
    const undoStatus = currentPaths.length > 0 || resetStack.length > 0;
    const redoStatus = undoStack.length > 0;
    onUndoRedoChange?.({ undo: undoStatus, redo: redoStatus });
  }, [undoStack.length, currentPaths.length]);


  React.useImperativeHandle(ref, () => ({
    eraseMode: (erase: boolean): void => {
      setDrawMode(!erase);
    },
    clearCanvas: (): void => {
      setResetStack([...currentPaths]);
      setCurrentPaths([]);
    },
    undo: (): void => {
      // If there was a last reset then
      if (resetStack.length !== 0) {
        setCurrentPaths([...resetStack]);
        setResetStack([]);

        return;
      }
      console.log("undo", currentPaths);
      setUndoStack((undoStack) => [...undoStack, ...currentPaths.slice(-1)]);
      setCurrentPaths((currentPaths) => currentPaths.slice(0, -1));
    },
    redo: (): void => {
      // Nothing to Redo
      if (undoStack.length === 0) return;

      setCurrentPaths((currentPaths) => [
        ...currentPaths,
        ...undoStack.slice(-1),
      ]);
      setUndoStack((undoStack) => undoStack.slice(0, -1));
    },
    exportImage: (params: ExportImageParams): Promise<string> => {
      const exportImage = svgCanvas.current?.exportImage;

      if (!exportImage) {
        throw Error('Export function called before canvas loaded');
      } else {
        return exportImage(params);
      }
    },
    exportSvg: (): Promise<string> => {
      return new Promise<string>((resolve, reject) => {
        const exportSvg = svgCanvas.current?.exportSvg;

        if (!exportSvg) {
          reject(Error('Export function called before canvas loaded'));
        } else {
          exportSvg()
            .then((data) => {
              resolve(data);
            })
            .catch((e) => {
              reject(e);
            });
        }
      });
    },
    exportPaths: (): Promise<CanvasPath[]> => {
      return new Promise<CanvasPath[]>((resolve, reject) => {
        try {
          resolve(currentPaths);
        } catch (e) {
          reject(e);
        }
      });
    },
    loadPaths: (paths: CanvasPath[]): void => {
      // setCurrentPaths((currentPaths) => [...currentPaths, ...paths]);
      setCurrentPaths((currentPaths) => [...paths, ...currentPaths]);
    },
    getSketchingTime: (): Promise<number> => {
      return new Promise<number>((resolve, reject) => {
        if (!withTimestamp) {
          reject(new Error("Set 'withTimestamp' prop to get sketching time"));
        }

        try {
          const sketchingTime = currentPaths.reduce(
            (totalSketchingTime, path) => {
              const startTimestamp = path.startTimestamp ?? 0;
              const endTimestamp = path.endTimestamp ?? 0;

              return totalSketchingTime + (endTimestamp - startTimestamp);
            },
            0
          );

          resolve(sketchingTime);
        } catch (e) {
          reject(e);
        }
      });
    },
    resetCanvas: (): void => {
      setResetStack([]);
      setUndoStack([]);
      setCurrentPaths([]);
    },
  }));

  const handlePointerDown = (point: Point): void => {
    if (dubblePointTimer.current) return;
    dubblePointTimer.current = setTimeout(() => {
      setIsDrawing(true);
      setUndoStack([]);

      let stroke: CanvasPath = {
        drawMode: drawMode,
        strokeColor: drawMode ? strokeColor : '#000000', // Eraser using mask
        strokeWidth: drawMode ? strokeWidth : eraserWidth,
        paths: [point],
        id: uuidv4(),
      };

      if (withTimestamp) {
        stroke = {
          ...stroke,
          startTimestamp: Date.now(),
          endTimestamp: 0,
        };
      }

      // setCurrentPaths((currentPaths) => [...currentPaths, stroke]);
      setNewPaths(stroke);
      // 开始绘制
      onStroke(stroke, !stroke.drawMode);
    }, 100);
  };

  const resetPointer = (): void => {
    clearTimeout(dubblePointTimer.current);
    dubblePointTimer.current = null;
    setIsDrawing(false);
    setNewPaths(null);
  };

  const handlePointerMove = (point: Point): void => {
    if (!isDrawing) return;

    const currentStroke = newPaths;
    const updatedStroke = {
      ...currentStroke,
      paths: [...currentStroke.paths, point],
    };

    setNewPaths(updatedStroke);
    // onStroke(updatedStroke, !updatedStroke.drawMode);
    // setCurrentPaths((currentPaths) => [
    //   ...currentPaths.slice(0, -1),
    //   updatedStroke,
    // ]);
  };

  const handlePointerUp = (): void => {
    if (dubblePointTimer.current) {
      resetPointer();
      return;
    }
    if (!isDrawing) {
      return;
    }

    if (!withTimestamp) {
      setIsDrawing(false);
      return;
    }

    // let currentStroke = currentPaths.slice(-1)?.[0] ?? null;
    let currentStroke = newPaths;

    if (currentStroke === null) {
      setIsDrawing(false);
      return;
    }

    const updatedStroke = {
      ...currentStroke,
      endTimestamp: Date.now(),
    };

    setCurrentPaths((currentPaths) => [
      ...currentPaths,
      updatedStroke,
    ]);
    onStroke(updatedStroke, !updatedStroke.drawMode);
    setIsDrawing(false);
    setNewPaths(null);
  };

  return (
    <Canvas
      ref={svgCanvas}
      id={id}
      width={width}
      height={height}
      className={className}
      canvasColor={canvasColor}
      backgroundImage={backgroundImage}
      exportWithBackgroundImage={exportWithBackgroundImage}
      preserveBackgroundImageAspectRatio={preserveBackgroundImageAspectRatio}
      allowOnlyPointerType={allowOnlyPointerType}
      style={style}
      svgStyle={svgStyle}
      paths={[...currentPaths, ...(newPaths ? [newPaths] : [])]}
      isDrawing={isDrawing}
      onPointerDown={handlePointerDown}
      onPointerMove={handlePointerMove}
      onPointerUp={handlePointerUp}
      resetPointer={resetPointer}
    />
  );
});
ReactSketchCanvas.displayName = 'ReactSketchCanvas';