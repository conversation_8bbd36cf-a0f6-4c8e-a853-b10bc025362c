export type ExportImageType = 'jpeg' | 'png' | 'webp';

export interface ExportImageParams {
  imageType?: ExportImageType;
  quality?: number;
  trim?: boolean;
}

export interface Point {
  readonly x: number;
  readonly y: number;
}

export interface CanvasPath {
  readonly paths: Point[];
  readonly strokeWidth: number;
  readonly strokeColor: string;
  readonly drawMode: boolean;
  readonly startTimestamp?: number;
  readonly endTimestamp?: number;
}
