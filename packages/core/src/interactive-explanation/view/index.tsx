"use client";

import Button from "@repo/ui/components/press-button";
import { AnimatePresence, motion } from "framer-motion";
import React, { useMemo } from "react";
import { EntryButton } from "../components/entry-button";
import { FloatingPanel } from "../components/floating-panel";
import StageNavigation from "../components/stage-navigation";
import { InteractiveExplanationProps, StageType } from "../types";

// 导入SVG图标
import { StudyType } from "@repo/core/enums";
import { cn } from "@repo/ui/lib/utils";
import {
  InteractiveExplanationProvider,
  useInteractiveExplanationContext,
} from "../context";
import ExamPointAnalysisStage from "./stages/exam-point-analysis-stage";
import ProblemAnalysisStage from "./stages/problem-analysis-stage";
import SolutionSummaryStage from "./stages/solution-summary-stage";
import StepByStepGuideStage from "./stages/step-by-step-guide-stage";

const InteractiveExplanationContent = () => {
  const {
    currentStage,
    isVisible,
    currentRecord,
    contentKey,
    showNextBtn,
    handleStageChange,
    handleStepProgress,
    handleAnswer,
    handleNext,
    handleAnimationComplete,
    trackEnter,
    trackExit,
    setIsVisible,
    onClose,
    initialData,
  } = useInteractiveExplanationContext();

  const headerMemo = useMemo(() => {
    return (
      <StageNavigation
        currentStage={currentStage}
        onStageChange={handleStageChange}
        stepCount={initialData?.stepByStepGuide?.steps.length ?? 0}
        className="interactive-explanation-navigation"
      />
    );
  }, [
    currentStage,
    handleStageChange,
    initialData?.stepByStepGuide?.steps.length,
  ]);

  const nextBtnMemo = useMemo(() => {
    if (!showNextBtn.value || currentStage === StageType.SolutionSummary)
      return null;
    return (
      <AnimatePresence>
        <motion.div
          className="bg-white flex p-8 justify-end"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.4, ease: "easeOut" }}
        >
          <Button
            color="orange"
            className="floating-panel-next-btn interactive-explanation-next-button"
            onClick={handleNext} // Disable logic remains the same
          >
            下一步
          </Button>
        </motion.div>
      </AnimatePresence>
    );
  }, [handleNext, showNextBtn.value, currentStage]);
  return (
    <>
      <EntryButton
        onEnter={() => {
          trackEnter();
          setIsVisible(true);
        }}
      />
      <FloatingPanel
        isVisible={isVisible}
        onClose={() => {
          trackExit();
          setIsVisible(false);

          onClose?.();
        }}
        className={cn("interactive-explanation-panel")}
        contentKey={contentKey}
        header={headerMemo}
        footer={nextBtnMemo}
      >
        {isVisible && (
          <div className="px-6 pb-6 interactive-explanation-content">
            {currentStage === StageType.ProblemAnalysis && (
              <ProblemAnalysisStage
                guideText={currentRecord.problemAnalysisGuideText ?? ""}
                data={initialData.problemAnalysis}
                onAnimationComplete={handleAnimationComplete}
              />
            )}
            {currentStage === StageType.ExamPointAnalysis && (
              <ExamPointAnalysisStage
                guideText={currentRecord.examPointAnalysisGuideText ?? ""}
                data={initialData.examPointAnalysis}
                onAnimationComplete={handleAnimationComplete}
              />
            )}
            {currentStage === StageType.StepByStepGuide && (
              <StepByStepGuideStage
                data={initialData.stepByStepGuide}
                record={currentRecord}
                onStepProgress={handleStepProgress}
                onAnswer={handleAnswer}
                onAnimationComplete={handleAnimationComplete}
              />
            )}
            {currentStage === StageType.SolutionSummary && (
              <SolutionSummaryStage
                guideText={currentRecord.solutionSummaryGuideText ?? ""}
                data={initialData.solutionSummary}
                onAnimationComplete={handleAnimationComplete}
              />
            )}
          </div>
        )}
      </FloatingPanel>
    </>
  );
};

/**
 * 互动讲题主组件
 * 整合aiExplanation数据和aiExplanationRecord记录
 */
export const InteractiveExplanation: React.FC<InteractiveExplanationProps> = ({
  onClose,
  initialData,
  initialRecord,
  onProgressChange,
  onAnswer,
  questionId,
  studySessionId = 0,
  preview = false,
  studyType = StudyType.AI_COURSE,
  trackEvent,
}) => {
  return (
    <InteractiveExplanationProvider
      onClose={onClose}
      questionId={questionId}
      studySessionId={studySessionId}
      preview={preview}
      studyType={studyType}
      initialData={initialData}
      initialRecord={initialRecord}
      onProgressChange={onProgressChange}
      onAnswer={onAnswer}
      trackEvent={trackEvent}
    >
      <InteractiveExplanationContent />
    </InteractiveExplanationProvider>
  );
};

InteractiveExplanation.displayName = "InteractiveExplanation";
