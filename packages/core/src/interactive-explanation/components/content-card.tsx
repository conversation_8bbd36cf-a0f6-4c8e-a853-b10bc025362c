"use client";

import { cn } from "@repo/ui/lib/utils";
import React, { useEffect, useState } from "react";

import TitleUnderline from "@repo/core/public/assets/interactive-explanation/title-underline.svg";

import { playAudioEffect } from "@repo/core/new-exercise/components";
import { audioFiles } from "@repo/core/new-exercise/components/header/ProgressBar/config";
import { CommonQuestionAnswerOption } from "@repo/core/types";
import EnhancedFormatMath from "./enhanced-format-math";
// 导入音效播放函数
// 问题数据接口
interface QuestionData {
  questionStem: string; // 问题描述
  questionOptionList: CommonQuestionAnswerOption[]; // 选项列表
  correctAnswers: string[]; // 正确答案列表，简化为字符串数组
}

// 答题配置接口
interface AnswerConfig {
  questionData: QuestionData; // 问题数据
  onCorrectAnswer?: () => void; // 答对回调
  onWrongAnswer?: (selectedKey: string) => void; // 答错回调
  // 新增：状态保持支持
  initialSelectedOption?: string; // 初始选中选项
  initialIsCorrect?: boolean | null; // 初始正确状态
  stepId?: string; // 步骤标识
}

interface ContentCardProps {
  title?: string;
  content?: string;
  className?: string;
  icon?: React.ReactNode;
  // 问题选项支持
  mode?: "content" | "question"; // 内容模式 或 问题模式
  answerConfig?: AnswerConfig; // 答题配置（集中管理）
  // 列表类型控制
  listType?: "ordered" | "unordered" | "auto"; // 强制指定列表类型，默认auto
}

// 答案选择组件（内部组件）
const AnswerSelector: React.FC<{
  questionData: QuestionData;
  onCorrectAnswer?: () => void;
  onWrongAnswer?: (selectedKey: string) => void;
  // 新增：外部状态支持
  initialSelectedOption?: string;
  initialIsCorrect?: boolean | null;
  stepId?: string; // 用于标识不同步骤的答题状态
}> = ({
  questionData,
  onCorrectAnswer,
  onWrongAnswer,
  initialSelectedOption = "",
  initialIsCorrect = null,
  stepId,
}) => {
  const [selectedOption, setSelectedOption] = useState<string>(
    initialSelectedOption
  );
  const [isCorrect, setIsCorrect] = useState<boolean | null>(initialIsCorrect);
  const [shakeOption, setShakeOption] = useState<string>("");

  // 同步外部状态变化
  useEffect(() => {
    setSelectedOption(initialSelectedOption);
    setIsCorrect(initialIsCorrect);
  }, [initialSelectedOption, initialIsCorrect, stepId]);

  // 处理选项点击
  const handleOptionClick = (optionKey: string) => {
    if (isCorrect) return; // 已经答对了，不允许再选择

    setSelectedOption(optionKey);

    // 检查答案
    const correct = questionData.correctAnswers.includes(optionKey);

    if (correct) {
      setIsCorrect(true);
      playAudioEffect(audioFiles.correct);
      onCorrectAnswer?.();
    } else {
      setIsCorrect(false);
      setShakeOption(optionKey);
      playAudioEffect(audioFiles.incorrect);
      onWrongAnswer?.(optionKey);

      // 摇晃动画后清除
      setTimeout(() => {
        setShakeOption("");
        setSelectedOption(""); // 清除错误选择，允许重新选择
        setIsCorrect(null);
      }, 600);
    }
  };

  // 获取选项状态
  const getOptionState = (optionKey: string) => {
    if (isCorrect && selectedOption === optionKey) return "correct";
    if (!isCorrect && selectedOption === optionKey) return "incorrect";
    return "normal";
  };

  return (
    <div className="content-card-answer-selector">
      {/* 问题描述 */}
      <div className="mb-4 content-card-question-stem">
        <EnhancedFormatMath
          htmlContent={questionData.questionStem}
          className="text-[17px] text-[rgba(51,48,45,0.95)] leading-[1.75em]"
        />
      </div>

      {/* 选项列表 */}
      <div className="flex flex-col max-w-full w-[max-content] gap-y-3 content-card-question-options">
        {questionData.questionOptionList.map((option) => {
          const state = getOptionState(option.optionKey || "");
          const shouldShake = shakeOption === option.optionKey;

          return (
            <div
              key={option.optionKey}
              className={cn(
                "content-card-question-option relative min-h-[3.375rem] w-full cursor-pointer gap-3 rounded-lg py-3 pl-5 pr-12 transition-all duration-200 [&_.katex-html]:!font-[700]",
                "min-w-1/2",
                // 基础样式
                "border",
                // 状态样式
                state === "correct" && "border-[rgba(132,214,75,0.6)]",
                state === "incorrect" && "border-[rgba(255,123,89,0.6)]",
                state === "normal" &&
                  "border-[rgba(51,48,45,0.1)] bg-white hover:border-[rgba(51,48,45,0.2)]",
                // 摇晃动画
                shouldShake && "animate-shake"
              )}
              style={{
                animation: shouldShake ? "shake 0.6s ease-in-out" : undefined,
                background:
                  state === "correct"
                    ? "linear-gradient(to right, #E5FFD1, #F4FAEE)"
                    : state === "incorrect"
                      ? "linear-gradient(to right, #FFE5DE, #FFF6F2)"
                      : undefined,
              }}
              onClick={() => handleOptionClick(option.optionKey || "")}
            >
              {/* 选项标签和内容 */}
              <div className="h-full w-full gap-3 content-card-option-content inline-flex items-start">
                <span className="font-bold w-[max-content] text-text-2 text-[17px] leading-[190%] content-card-option-key">
                  {option.optionKey}
                </span>
                <div className="w-[max-content] content-card-option-text">
                  <EnhancedFormatMath
                    htmlContent={option.optionVal || ""}
                    className="font-bold text-[17px] text-[rgba(51,48,45,0.85)] leading-[1.75em]"
                  />
                </div>
              </div>

              {/* 状态指示器 */}
              <div className="top-1/2 right-5 -translate-y-1/2 content-card-option-indicator absolute">
                {state === "correct" && (
                  <div className="rounded-full flex h-6 w-6 content-card-correct-indicator items-center justify-center">
                    <div className="rounded-full flex bg-[#58CC02] h-5 w-5 items-center justify-center">
                      <svg
                        width="9.5"
                        height="6"
                        viewBox="0 0 9.5 6"
                        fill="none"
                      >
                        <path
                          d="M1 3L3.5 5.5L8.5 0.5"
                          stroke="white"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                )}
                {state === "incorrect" && (
                  <div className="rounded-full flex h-6 w-6 content-card-incorrect-indicator items-center justify-center">
                    <div className="rounded-full flex bg-[#FF6139] h-5 w-5 items-center justify-center">
                      <svg width="8" height="8" viewBox="0 0 8 8" fill="none">
                        <path
                          d="M1 1L7 7M7 1L1 7"
                          stroke="white"
                          strokeWidth="2"
                          strokeLinecap="round"
                        />
                      </svg>
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* 摇晃动画样式 */}
      <style
        dangerouslySetInnerHTML={{
          __html: `
          @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
          }
        `,
        }}
      />
    </div>
  );
};

export const ContentCard: React.FC<ContentCardProps> = ({
  title,
  content,
  className,
  icon,
  mode = "content",
  answerConfig,
  listType = "auto",
}) => {
  // 渲染问题选项
  const renderQuestionOptions = () => {
    if (!answerConfig) return null;

    return (
      <div className="w-full content-card-question-content">
        <AnswerSelector
          questionData={answerConfig.questionData}
          onCorrectAnswer={answerConfig.onCorrectAnswer}
          onWrongAnswer={answerConfig.onWrongAnswer}
          initialSelectedOption={answerConfig.initialSelectedOption}
          initialIsCorrect={answerConfig.initialIsCorrect}
          stepId={answerConfig.stepId}
        />
      </div>
    );
  };

  const renderContent = () => {
    // 问题模式
    if (mode === "question") {
      return renderQuestionOptions();
    }

    // 内容模式 - 使用新的MarkdownListRenderer
    if (!content) return null;

    // 使用EnhancedFormatMath处理所有内容（包括列表、加粗、数学公式等）
    return (
      <EnhancedFormatMath
        htmlContent={content}
        className="content-card-rendered-content"
        listType={listType}
      />
    );
  };

  return (
    <div
      className={cn(
        "content-card flex w-full flex-col items-start gap-6 rounded-[1.25rem] bg-[#F7F6F5] p-6",
        className
      )}
    >
      {title && (
        <div className="flex gap-2 content-card-header items-center">
          {icon && <span className="content-card-icon">{icon}</span>}
          <h3 className="font-bold text-text-1 text-[1.5rem] leading-[1.25em] z-10 relative">
            <span className="z-10 relative">{title}</span>
            <div className="w-full right-0 bottom-[-2px] z-0 content-card-title-underline absolute">
              <TitleUnderline className="w-full" />
            </div>
          </h3>
        </div>
      )}

      <div className="w-full content-card-body">{renderContent()}</div>
    </div>
  );
};

export default ContentCard;
