import { GuidePlayer } from "@repo/core/components/player/guide-player";
import { StudyType } from "@repo/core/enums";
import { COMPLETE_EXERCISE_VARIABLES } from "@repo/core/new-exercise/context/theme-context/exercise-theme-variables";
import { ExerciseView } from "@repo/core/new-exercise/view/main-view";
import { Loading } from "@repo/ui/components/loading";
import { FC } from "react";
import { usePreviewCtx } from "../_helper";
import { InteractiveView } from "./interactive/interactive-view";

export const Main: FC = () => {
  const { widget, widgets, loading, simulation } = usePreviewCtx();

  return (
    <div className="main h-full w-full">
      {loading ? (
        <div className="lesson_preview_loading_container flex h-full w-full items-center justify-center">
          <Loading
            variant="orbit"
            size="lg"
            text="正在加载课程内容..."
            className="lesson_preview_loading"
          />
        </div>
      ) : (
        <>
          {widget.widgetType === "guide" && (
            <div className="h-[600px] w-[1000px] overflow-auto">
              <GuidePlayer
                data={widget.data}
                width={1000}
                height={600}
                className="h-full w-full"
                partIndex={widget.widgetIndex}
                totalPartsCount={widgets.length}
              />
            </div>
          )}

          {widget.widgetType === "exercise" && (
            <div className="h-8/10 w-full" style={{ zoom: 0.73 }}>
              {widget.data.length > 0 ? (
                <ExerciseView
                  studySessionId={1} // 🆕 必需：使用非0值避免验证错误
                  studyType={StudyType.REINFORCEMENT_EXERCISE} // 🔄 保持一致
                  clientContext={null} // 🆕 必需：预览模式下设为null
                  isPreview={true} // 🆕 必需
                  previewConfig={{
                    // 🔑 修复：提取 questionInfo 数据，转换为新组件期望的格式
                    questionList: widget.data.map(
                      (item: any) => item.questionInfo
                    ),
                    studentAnswers: [], // 🆕 产课工具通常没有学生答题记录
                    initialIndex: 0,
                    studentMode: false, // 🔑 关键：禁用学生专用功能
                  }}
                  displayConfig={{
                    // 🆕 新增：显示配置
                    progressBar: {
                      hidden: false,
                      showMode: "number", // 产课工具使用数字模式
                      totalCount: widget.data.length,
                    },
                    timer: {
                      hidden: true, // 预览模式隐藏计时器
                    },
                  }}
                  customVariables={COMPLETE_EXERCISE_VARIABLES} // 🔄 样式隔离保持不变
                  onBack={() => window.history.back()}
                  // showExplanations 在新版中自动处理
                />
              ) : (
                <div className="m-60">暂无数据</div>
              )}
            </div>
          )}

          {widget.widgetType === "interactive" && (
            <div className="overflow-y-auto p-1">
              {widget.data.url ? (
                <div className="h-[600px] w-[960px] overflow-y-auto px-4">
                  <InteractiveView
                    active={true}
                    index={widget.index}
                    url={widget.data.url}
                    type={widget.data.typeName}
                    onReport={(e) => {
                      console.log(e);
                    }}
                  />
                </div>
              ) : (
                <div className="flex h-full w-full items-center justify-center">
                  暂未配置，请前去设置
                </div>
              )}
            </div>
          )}

          {/* TODO: 接入视频组件 */}
          {widget.widgetType === "video" && (
            <div
              className={
                simulation
                  ? "flex h-full w-full items-center bg-black"
                  : "h-full w-full"
              }
            >
              <video src={widget?.data?.url} controls></video>
            </div>
          )}
        </>
      )}
    </div>
  );
};
