"use client";

import Interactive from "@repo/core/components/interactive-component";
import { StudyType } from "@repo/core/enums";
import { CourseWidgetDetail } from "../type";

import { ExerciseDisplayConfig } from "@repo/core/new-exercise/types/exercise-config";
import { ExerciseView } from "@repo/core/new-exercise/view/main-view";
import { cn } from "@repo/ui/lib/utils";
import { memo, useEffect, useMemo, useState } from "react";
import { GuidePlayer } from "../GuidePlayer";
import "./index.css";

const MemoizedExerciseView = memo(ExerciseView);

export const WidgetLoader = ({
  data,
  width,
  height,
  guideStyle,
  guideInputProps,
  onQuestionChange,
  guideWidgetProps,
  handlerRef,
  className,
}: {
  data?: CourseWidgetDetail;
  width: number;
  height: number;
  guideStyle?: React.CSSProperties;
  guideInputProps?: React.ComponentProps<typeof GuidePlayer>["inputProps"];
  onQuestionChange?: React.ComponentProps<
    typeof ExerciseView
  >["onQuestionChange"];
  guideWidgetProps?: Partial<
    Omit<
      React.ComponentProps<typeof GuidePlayer>,
      "data" | "width" | "height" | "className"
    >
  >;
  handlerRef?: React.RefObject<{
    refresh: () => void;
  } | null>;
  className?: string;
}) => {
  const [mainKey, setMainKey] = useState(0);

  useEffect(() => {
    if (handlerRef) {
      handlerRef.current = {
        refresh: () => {
          setMainKey((prev) => prev + 1);
        },
      };
    }
  }, [handlerRef, setMainKey]);

  const previewConfig = useMemo(() => {
    if (data?.type !== "exercise") return {};
    return {
      questionList: data.data.map((e) => e.questionInfo),
      studentMode: false,
    };
  }, [data]);

  const displayConfig = useMemo<ExerciseDisplayConfig>(() => {
    if (data?.type !== "exercise") return {};

    return {
      progressBar: {
        hidden: false,
        showMode: "number",
        totalCount: data.data.length,
      },
      timer: {
        hidden: true,
      },
    };
  }, [data]);

  if (!data) return <div className="h-full w-full bg-red-300">NO DATA</div>;

  return (
    <>
      {/* 文稿组件 */}
      {data.type === "guide" && (
        <div className="ai-course-preview-guide-view h-full w-full flex-1">
          <GuidePlayer
            key={`${data.type}-${data.name}-${mainKey}`}
            data={data.data}
            className={cn("h-full w-full", className)}
            width={width}
            height={height}
            {...guideWidgetProps}
            inputProps={guideInputProps}
            style={guideStyle}
          />
        </div>
      )}

      {/* 练习组件 */}
      {data.type === "exercise" && (
        <div
          className="ai-course-preview-exercise-view h-full w-full"
          style={{ zoom: 0.73 }}
        >
          {(data.data.length || 0) > 0 ? (
            // 这TM到底是个业务组件还是展示组件？
            <MemoizedExerciseView
              key={`${data.type}-${data.name}-${mainKey}`}
              isPreview
              studyType={StudyType.REINFORCEMENT_EXERCISE}
              // ？？？
              previewConfig={previewConfig}
              displayConfig={displayConfig}
              clientContext={null}
              studySessionId={0}
              onQuestionChange={onQuestionChange}
              onBack={() => false}
              onToggleWrongQuestionBook={undefined}
              // customVariables={COMPLETE_EXERCISE_VARIABLES} // 🔥 样式隔离
            />
          ) : (
            <div className="mt-6 flex h-full w-full items-center justify-center">
              暂无数据
            </div>
          )}
        </div>
      )}

      {/* 视频组件 */}
      {data.type === "video" && (
        <div className="h-full">
          <video src={data.data.url} controls className="h-full w-full"></video>
        </div>
      )}

      {data.type === "interactive" && (
        <div className="h-full w-full overflow-y-auto">
          <Interactive
            key={`${data.type}-${data.data.typeName}-${mainKey}`}
            url={data.data.url}
            type={data.data.typeName}
            onReport={(e) => {
              console.log(e);
            }}
          >
            <div>loading...</div>
          </Interactive>
        </div>
      )}
    </>
  );
};
