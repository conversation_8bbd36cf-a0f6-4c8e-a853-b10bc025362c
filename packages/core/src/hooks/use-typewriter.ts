import { useCallback } from "react";
// import { Conversation } from "../components/common/message-list";

interface TypewriterOptions {
  speed?: number;
  onComplete?: () => void;
}

export const useTypewriter = () => {
  // 添加打字机效果的方法
  const typeMessage = useCallback(
    async (
      text: string,
      setMessageList: React.Dispatch<React.SetStateAction<any[]>>,
      options: TypewriterOptions = {}
    ) => {
      const { speed = 30, onComplete } = options;

      // 安全检查：确保 text 存在
      if (!text || typeof text !== 'string') {
        onComplete?.();
        return;
      }

      let currentText = "";

      // 先创建一个空消息
      setMessageList((prev) => {
        const newList = [...prev];
        newList.pop(); // 移除思考中的消息
        return [
          ...newList,
          {
            message: currentText,
            type: "dolli",
            time: Date.now().toString(),
            isTyping: true, // 添加打字状态标记
          },
        ];
      });

      // 逐字打印
      for (let i = 0; i < text.length; i++) {
        await new Promise((resolve) => setTimeout(resolve, speed));
        currentText += text[i];

        setMessageList((prev) => {
          const newList = [...prev];
          const lastMessage = newList[newList.length - 1];
          if (lastMessage) {
            lastMessage.message = currentText;
          }
          return newList;
        });
      }

      // 完成打字，更新状态
      setMessageList((prev) => {
        const newList = [...prev];
        const lastMessage = newList[newList.length - 1];
        if (lastMessage) {
          lastMessage.isTyping = false;
        }
        return newList;
      });

      onComplete?.();
    },
    []
  );

  return { typeMessage };
};
