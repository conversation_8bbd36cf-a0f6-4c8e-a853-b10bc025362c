import {
  RefObject,
  useState,
  useRef,
  useMemo,
  useCallback,
  useEffect,
} from "react";

// 定义滚动方向类型
type ScrollDirection = "top" | "right" | "bottom" | "left";

// 定义方向配置对象接口
interface DirectionConfig {
  // 监控的方向
  direction: ScrollDirection;
  // 阈值，范围0-1，数字越大表示需要更多区域可见才触发
  threshold?: number;
  // 当滚动状态改变时的回调函数
  onChange?: (isVisible: boolean) => void;
}

/**
 * useScrollDetection - 检测元素滚动状态的自定义Hook
 *
 * @description
 * 该Hook用于检测容器是否滚动到指定的边缘，支持4个方向的检测：上、右、下、左
 * 每个方向可以有自己独立的配置，包括阈值和回调函数
 * 该Hook使用refs直接引用DOM元素，避免了使用querySelector带来的多实例冲突
 *
 * @example
 * // 基本用法
 * const { sentinels, hasOverflow } = useScrollDetection(containerRef, [
 *   {
 *     direction: 'bottom',
 *     threshold: 0.1,
 *     onChange: (isVisible) => setShowGradient(!isVisible)
 *   }
 * ]);
 *
 * // 在JSX中使用
 * <div ref={containerRef}>
 *   {content}
 *   {sentinels.bottom}
 * </div>
 *
 * @param containerRef - 要监听的容器元素引用
 * @param configs - 方向配置对象数组，每个方向设置独立的阈值和回调
 * @returns 包含哨兵元素和溢出状态的对象
 */
export function useScrollDetection<T extends HTMLElement>(
  containerRef: RefObject<T | null>,
  configs: DirectionConfig[] = [{ direction: "bottom" }]
) {
  // 记录各方向的滚动状态
  const [scrollState, setScrollState] = useState<
    Record<ScrollDirection, boolean>
  >({
    top: false,
    right: false,
    bottom: false,
    left: false,
  });

  // 在顶层直接创建ref，而不是在useMemo内创建
  const topRef = useRef<HTMLDivElement>(null);
  const rightRef = useRef<HTMLDivElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);
  const leftRef = useRef<HTMLDivElement>(null);

  // 收集所有ref到一个对象中
  const sentinelRefs = useMemo(
    () => ({
      top: topRef,
      right: rightRef,
      bottom: bottomRef,
      left: leftRef,
    }),
    []
  );

  // 记录配置映射，用于快速查找
  const configMap = useMemo(() => {
    const map: Record<ScrollDirection, DirectionConfig> = {
      top: { direction: "top", threshold: 0.1 },
      right: { direction: "right", threshold: 0.1 },
      bottom: { direction: "bottom", threshold: 0.1 },
      left: { direction: "left", threshold: 0.1 },
    };

    // 使用用户提供的配置覆盖默认值
    configs.forEach((config) => {
      map[config.direction] = {
        ...map[config.direction],
        ...config,
      };
    });

    return map;
  }, [configs]);

  // 获取所有需要监控的方向
  const directions = useMemo(() => {
    return configs.map((config) => config.direction);
  }, [configs]);

  // 记录容器是否有溢出内容（是否可滚动）
  const [hasOverflow, setHasOverflow] = useState<Record<"x" | "y", boolean>>({
    x: false,
    y: false,
  });

  // 创建和更新状态的处理函数
  const updateScrollState = useCallback(
    (direction: ScrollDirection, isVisible: boolean) => {
      // 更新内部状态
      setScrollState((prev) => {
        // 仅在值实际变化时更新状态
        if (prev[direction] !== isVisible) {
          return { ...prev, [direction]: isVisible };
        }
        return prev;
      });

      // 调用用户提供的回调函数
      const config = configMap[direction];
      if (config?.onChange) {
        config.onChange(isVisible);
      }
    },
    [configMap]
  );

  // 创建哨兵元素
  const sentinels = useMemo(
    () => ({
      top: <div ref={topRef} className="sentinel-top h-[1px] w-full" />,
      right: <div ref={rightRef} className="sentinel-right h-full w-[1px]" />,
      bottom: (
        <div ref={bottomRef} className="sentinel-bottom h-[1px] w-full" />
      ),
      left: <div ref={leftRef} className="sentinel-left h-full w-[1px]" />,
    }),
    []
  );

  // 监听滚动和溢出
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // 检查容器是否有溢出内容的函数
    const checkOverflow = () => {
      setHasOverflow((prev) => {
        const newState = {
          x: container.scrollWidth > container.clientWidth,
          y: container.scrollHeight > container.clientHeight,
        };

        // 仅在值实际变化时更新状态
        if (prev.x !== newState.x || prev.y !== newState.y) {
          return newState;
        }
        return prev;
      });
    };

    // 立即检查一次
    checkOverflow();

    // 观察器数组，用于清理
    const observers: IntersectionObserver[] = [];

    // 只为配置的方向创建观察器
    directions.forEach((direction) => {
      const sentinelRef = sentinelRefs[direction];
      const sentinel = sentinelRef.current;
      if (!sentinel) return;

      const config = configMap[direction];
      const threshold = config.threshold || 0.1;

      // 创建IntersectionObserver实例
      const observer = new IntersectionObserver(
        (entries) => {
          const isVisible = entries[0]?.isIntersecting ?? false;

          updateScrollState(direction, isVisible);
        },
        {
          root: container,
          threshold,
        }
      );

      // 开始观察
      observer.observe(sentinel);
      observers.push(observer);
    });

    // 使用ResizeObserver监听容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      requestAnimationFrame(checkOverflow);
    });

    resizeObserver.observe(container);

    // 清理函数
    return () => {
      observers.forEach((observer) => observer.disconnect());
      resizeObserver.disconnect();
    };
  }, [containerRef, directions, configMap, updateScrollState, sentinelRefs]);

  return {
    scrollState, // 所有方向的滚动状态
    sentinels, // 渲染用的哨兵元素
    hasOverflow, // 容器是否有溢出内容
    isAtEdge: (direction: ScrollDirection) => scrollState[direction], // 判断是否滚动到某个边缘的辅助函数
  };
}
