{"name": "@repo/ui", "version": "0.0.0", "type": "module", "private": true, "scripts": {}, "dependencies": {"@preact-signals/safe-react": "^0.9.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@xyflow/react": "^12.5.5", "better-react-mathjax": "^2.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.4.2", "lucide-react": "^0.475.0", "next-themes": "^0.4.6", "react": "catalog:react19", "react-day-picker": "8.10.1", "react-dom": "catalog:react19", "sonner": "^2.0.3", "tailwind-merge": "^3.0.1", "tw-animate-css": "^1.2.4", "zod": "^3.24.2"}, "devDependencies": {"@csstools/postcss-color-mix-function": "^3.0.10", "@csstools/postcss-oklab-function": "^4.0.8", "@repo/config-eslint": "workspace:*", "@repo/config-typescript": "workspace:*", "@tailwindcss/postcss": "^4.1.3", "@tailwindcss/typography": "^0.5.16", "@turbo/gen": "^2.4.2", "@types/node": "^20", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "postcss-nesting": "^13.0.2", "tailwindcss": "^4.1.3", "typescript": "^5.8.3"}, "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts", "./styles/font/resource-han-rounded.css": "./src/styles/font/resource-han-rounded.css"}}