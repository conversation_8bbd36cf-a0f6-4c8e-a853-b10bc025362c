---
description: 
globs: 
alwaysApply: true
---

# Schoolroom Monorepo

这是一个基于 Turborepo 构建的课堂管理系统 monorepo，包含以下主要组件：

## 应用和包

- `apps/stu` 学生端应用，基于 Next.js
- `apps/teacher`: 教师端应用，基于 Next.js
- `apps/aipt`: AI生产工具应用, 基于 Next.js
- `packages/lib`: 共享工具库, 被各个应用共同使用
- `packages/ui`: 共享UI库, 被各个应用共同使用
- `packages/core`: 共享业务库, 被各个应用共同使用
- `packages/config-eslint`: ESLint 配置，包含 next 和 prettier 配置
- `packages/config-typescript`: 整个 monorepo 使用的 TypeScript 配置

## 技术栈

- TypeScript 用于静态类型检查
- ESLint 用于代码检查
- Prettier 用于代码格式化
- Turborepo 用于 monorepo 管理
- Next.js 用于前端应用开发
- React Hooks + Context API + SWR 用于状态管理
- remtion.dev 动态视频 [https://www.remotion.dev/]
- swr Http请求 [https://swr.vercel.app/]
- tailwind.css css framework [https://tailwindcss.com/]

使用`MCP:Context7`查询具体文档

## 应用架构规范

- 具体见 [./application-architecture.md]

## 开发命令

- `pnpm build`: 构建所有应用和包
- `pnpm dev`: 启动所有应用和包的开发环境
